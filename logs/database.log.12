2025-06-19 17:13:03,127 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Event` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:03,303 WARNING database DDL Query made to DB:
ALTER TABLE `tabGrievance Type` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:03,511 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant` MODIFY `applicant_rating` decimal(3,2), MODIFY `lower_range` decimal(21,9) not null default 0, MODIFY `upper_range` decimal(21,9) not null default 0
2025-06-19 17:13:03,543 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:03,713 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:03,959 WARNING database DDL Query made to DB:
<PERSON>TER TABLE `tabAttendance` ADD COLUMN `half_day_status` varchar(140), ADD COLUMN `modify_half_day_status` int(1) not null default 0
2025-06-19 17:13:03,980 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0
2025-06-19 17:13:04,020 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:04,206 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Property History` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:04,360 WARNING database DDL Query made to DB:
ALTER TABLE `tabDaily Work Summary Group User` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:04,545 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Type` MODIFY `working_hours_threshold_for_half_day` decimal(21,9) not null default 0, MODIFY `working_hours_threshold_for_absent` decimal(21,9) not null default 0
2025-06-19 17:13:04,580 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Type` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:04,754 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Event Employee` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:04,924 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Ledger Entry` MODIFY `leaves` decimal(21,9) not null default 0
2025-06-19 17:13:04,957 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Ledger Entry` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:05,142 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:05,307 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill Map` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:05,455 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Grade` MODIFY `default_base_pay` decimal(21,9) not null default 0
2025-06-19 17:13:05,486 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Grade` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:05,692 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Assignment` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:05,889 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:06,088 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result Employee` MODIFY `hours` decimal(21,9) not null default 0
2025-06-19 17:13:06,124 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result Employee` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:06,303 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterviewer` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:06,518 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompensatory Leave Request` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:06,733 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan Detail` MODIFY `estimated_cost_per_position` decimal(21,9) not null default 0, MODIFY `total_estimated_cost` decimal(21,9) not null default 0
2025-06-19 17:13:06,764 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan Detail` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:06,937 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Type` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:07,112 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurpose of Travel` ADD UNIQUE INDEX IF NOT EXISTS purpose_of_travel (`purpose_of_travel`)
2025-06-19 17:13:07,143 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurpose of Travel` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:07,317 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Account` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:07,510 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Promotion` MODIFY `current_ctc` decimal(21,9) not null default 0, MODIFY `revised_ctc` decimal(21,9) not null default 0
2025-06-19 17:13:07,543 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Promotion` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:07,777 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Type` MODIFY `maximum_carry_forwarded_leaves` decimal(21,9) not null default 0, MODIFY `max_leaves_allowed` decimal(21,9) not null default 0, MODIFY `fraction_of_daily_salary_per_leave` decimal(21,9) not null default 0
2025-06-19 17:13:07,813 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Type` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:08,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Advance` MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `unclaimed_amount` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0
2025-06-19 17:13:08,037 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Advance` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:08,220 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Transfer` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:08,440 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Separation` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:08,679 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:08,872 WARNING database DDL Query made to DB:
ALTER TABLE `tabDesignation Skill` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:09,086 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Statement` MODIFY `total_receivable_amount` decimal(21,9) not null default 0, MODIFY `total_payable_amount` decimal(21,9) not null default 0, MODIFY `total_asset_recovery_cost` decimal(21,9) not null default 0
2025-06-19 17:13:09,126 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Statement` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:09,329 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-19 17:13:09,360 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:09,569 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Performance Feedback` MODIFY `total_score` decimal(21,9) not null default 0
2025-06-19 17:13:09,600 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Performance Feedback` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:09,939 WARNING database DDL Query made to DB:
ALTER TABLE `tabDaily Work Summary Group` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:10,110 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Health Insurance` ADD UNIQUE INDEX IF NOT EXISTS health_insurance_name (`health_insurance_name`)
2025-06-19 17:13:10,142 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Health Insurance` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:10,300 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Detail` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:10,443 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Rating` MODIFY `rating` decimal(3,2), MODIFY `per_weightage` decimal(21,9) not null default 0
2025-06-19 17:13:10,475 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Rating` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:10,657 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Program` ADD UNIQUE INDEX IF NOT EXISTS training_program (`training_program`)
2025-06-19 17:13:10,687 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Program` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:10,829 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template Goal` MODIFY `per_weightage` decimal(21,9) not null default 0
2025-06-19 17:13:10,868 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template Goal` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:11,879 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration Category` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_amount` decimal(21,9) not null default 0
2025-06-19 17:13:11,910 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration Category` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:12,065 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Category` MODIFY `max_amount` decimal(21,9) not null default 0
2025-06-19 17:13:12,097 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Category` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:12,306 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:12,484 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Other Income` MODIFY `amount` decimal(21,9) not null default 0
2025-06-19 17:13:12,517 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Other Income` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:12,707 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission` MODIFY `exemption_amount` decimal(21,9) not null default 0, MODIFY `total_actual_amount` decimal(21,9) not null default 0
2025-06-19 17:13:12,741 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:12,914 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Period Date` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:13,064 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application Detail` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0
2025-06-19 17:13:13,096 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application Detail` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:13,277 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Period` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:13,465 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission Detail` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_amount` decimal(21,9) not null default 0
2025-06-19 17:13:13,496 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission Detail` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:13,699 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure Assignment` MODIFY `base` decimal(21,9) not null default 0, MODIFY `custom_housing_allowance` decimal(21,9) not null default 0, MODIFY `custom_bonus` decimal(21,9) not null default 0, MODIFY `custom_salary_advance` decimal(21,9) not null default 0, MODIFY `tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `variable` decimal(21,9) not null default 0, MODIFY `custom_duty_allowance` decimal(21,9) not null default 0, MODIFY `custom_mid_month_advance` decimal(21,9) not null default 0, MODIFY `custom_transport_allowance` decimal(21,9) not null default 0, MODIFY `taxable_earnings_till_date` decimal(21,9) not null default 0
2025-06-19 17:13:13,736 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure Assignment` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:13,946 WARNING database DDL Query made to DB:
ALTER TABLE `tabRetention Bonus` MODIFY `bonus_amount` decimal(21,9) not null default 0
2025-06-19 17:13:13,980 WARNING database DDL Query made to DB:
ALTER TABLE `tabRetention Bonus` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:14,346 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0
2025-06-19 17:13:14,381 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:14,644 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Claim` MODIFY `max_amount_eligible` decimal(21,9) not null default 0, MODIFY `claimed_amount` decimal(21,9) not null default 0
2025-06-19 17:13:14,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Claim` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:14,910 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Incentive` MODIFY `incentive_amount` decimal(21,9) not null default 0
2025-06-19 17:13:14,942 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Incentive` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:15,137 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Cost Center` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:15,465 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component Account` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:15,640 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Employee Detail` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:15,850 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-19 17:13:15,885 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:16,115 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-06-19 17:13:16,150 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:16,324 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration` MODIFY `total_exemption_amount` decimal(21,9) not null default 0, MODIFY `total_declared_amount` decimal(21,9) not null default 0
2025-06-19 17:13:16,355 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:16,526 WARNING database DDL Query made to DB:
ALTER TABLE `tabTaxable Salary Slab` MODIFY `to_amount` decimal(21,9) not null default 0
2025-06-19 17:13:16,557 WARNING database DDL Query made to DB:
ALTER TABLE `tabTaxable Salary Slab` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:16,701 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Timesheet` MODIFY `working_hours` decimal(21,9) not null default 0
2025-06-19 17:13:16,733 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Timesheet` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:16,887 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab Other Charges` MODIFY `max_taxable_income` decimal(21,9) not null default 0, MODIFY `min_taxable_income` decimal(21,9) not null default 0, MODIFY `percent` decimal(21,9) not null default 0
2025-06-19 17:13:16,919 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab Other Charges` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:17,101 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `default_amount` decimal(21,9) not null default 0, MODIFY `additional_amount` decimal(21,9) not null default 0, MODIFY `tax_on_flexible_benefit` decimal(21,9) not null default 0, MODIFY `tax_on_additional_salary` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0
2025-06-19 17:13:17,153 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:17,332 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:17,499 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Applicable Component` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:17,651 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Leave` MODIFY `available_leaves` decimal(21,9) not null default 0, MODIFY `pending_leaves` decimal(21,9) not null default 0, MODIFY `total_allocated_leaves` decimal(21,9) not null default 0, MODIFY `expired_leaves` decimal(21,9) not null default 0, MODIFY `used_leaves` decimal(21,9) not null default 0
2025-06-19 17:13:17,684 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Leave` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:17,867 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application` MODIFY `remaining_benefit` decimal(21,9) not null default 0, MODIFY `pro_rata_dispensed_amount` decimal(21,9) not null default 0, MODIFY `max_benefits` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0
2025-06-19 17:13:17,899 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:18,157 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `max_benefit_amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-19 17:13:18,191 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:18,384 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab` ADD COLUMN `tax_relief_limit` decimal(21,9) not null default 0
2025-06-19 17:13:18,406 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab` MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0
2025-06-19 17:13:18,437 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:18,594 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Sub Category` MODIFY `max_amount` decimal(21,9) not null default 0
2025-06-19 17:13:18,629 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Sub Category` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:18,794 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule Slab` MODIFY `fraction_of_applicable_earnings` decimal(21,9) not null default 0
2025-06-19 17:13:18,826 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule Slab` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:19,051 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure` MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `leave_encashment_amount_per_day` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `max_benefits` decimal(21,9) not null default 0, MODIFY `total_earning` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0
2025-06-19 17:13:19,103 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Structure` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:20,116 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-19 17:13:20,286 WARNING database DDL Query made to DB:
create table `tabTRA TAX Inv` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`type` varchar(140) default 'Sales',
`verification_code` varchar(140) unique,
`verification_status` varchar(140) default 'Pending',
`verification_date` date,
`company_name` varchar(140),
`receipt_number` varchar(140),
`subtotal` decimal(21,9) not null default 0,
`total_tax` decimal(21,9) not null default 0,
`grand_total` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:13:20,557 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-19 17:13:20,687 WARNING database DDL Query made to DB:
create table `tabTRA TAX Inv Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`quantity` varchar(140),
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:13:21,686 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-19 17:13:21,953 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-19 17:13:22,624 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-19 17:13:22,973 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-19 17:13:23,570 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `posting_datetime_creation_index`
2025-06-19 17:13:23,605 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `item_warehouse`
2025-06-19 17:13:25,294 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-19 17:13:29,304 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` ADD COLUMN `custom_supplier_name` varchar(140)
2025-06-19 17:13:29,325 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` MODIFY `grand_total` decimal(21,9) not null default 0
2025-06-19 17:13:29,640 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `restrict_unallocated_amount_for_supplier` int(1) not null default 0
2025-06-19 17:13:29,669 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0
2025-06-19 17:13:29,745 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD COLUMN `restrict_unallocated_amount_for_supplier` int(1) not null default 0
2025-06-19 17:13:29,767 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0
2025-06-19 17:13:29,938 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0
2025-06-19 17:13:30,369 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-19 17:35:49,521 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-19 17:35:50,909 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-19 17:35:52,791 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-19 17:35:54,972 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-19 17:35:55,561 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-19 17:35:56,111 WARNING database DDL Query made to DB:
ALTER TABLE `tabStation Members` DROP INDEX `parent`
2025-06-19 17:35:56,474 WARNING database DDL Query made to DB:
ALTER TABLE `tabPiecework Payment Allocation` MODIFY `amount` decimal(21,9) not null default 0
2025-06-19 17:35:56,503 WARNING database DDL Query made to DB:
ALTER TABLE `tabPiecework Payment Allocation` DROP INDEX `parent`
2025-06-19 17:35:56,778 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-19 17:35:57,182 WARNING database DDL Query made to DB:
ALTER TABLE `tabSingle Piecework Employees` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `task_rate` decimal(21,9) not null default 0
2025-06-19 17:35:57,219 WARNING database DDL Query made to DB:
ALTER TABLE `tabSingle Piecework Employees` DROP INDEX `parent`
2025-06-19 17:35:57,446 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-19 17:35:57,748 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Employee Salary Slip` DROP INDEX `parent`
2025-06-19 17:35:58,843 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-19 17:35:59,120 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-19 17:35:59,835 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-19 17:36:00,028 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-19 17:36:01,769 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0
2025-06-19 17:36:06,815 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0
2025-06-19 17:45:34,136 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-19 17:45:35,123 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-19 17:45:36,220 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-19 17:45:37,413 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-19 17:45:37,724 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-19 17:45:38,180 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Piecework Additional Salary` MODIFY `amount` decimal(21,9) not null default 0
2025-06-19 17:45:38,207 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Piecework Additional Salary` DROP INDEX `parent`
2025-06-19 17:45:38,365 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-19 17:45:38,555 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-19 17:45:39,246 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-19 17:45:39,446 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-19 17:45:39,903 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-19 17:45:40,057 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-19 17:45:41,084 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0
2025-06-19 17:45:43,930 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0
2025-06-19 17:54:05,246 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-19 17:54:06,189 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-19 17:54:07,381 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-19 17:54:08,546 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-19 17:54:08,883 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-19 17:54:09,295 WARNING database DDL Query made to DB:
ALTER TABLE `tabRoot Cause Prevention Strategy` MODIFY `estimated_cost` decimal(21,9) not null default 0
2025-06-19 17:54:09,330 WARNING database DDL Query made to DB:
ALTER TABLE `tabRoot Cause Prevention Strategy` DROP INDEX `parent`
2025-06-19 17:54:09,580 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrice Change Request Detail` MODIFY `new_price` decimal(21,9) not null default 0, MODIFY `cost` decimal(21,9) not null default 0, MODIFY `old_price` decimal(21,9) not null default 0
2025-06-19 17:54:09,610 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrice Change Request Detail` DROP INDEX `parent`
2025-06-19 17:54:09,764 WARNING database DDL Query made to DB:
ALTER TABLE `tabEFD Z Report Invoice` MODIFY `vat` decimal(21,9) not null default 0, MODIFY `amt_ex__sr` decimal(21,9) not null default 0, MODIFY `amt_excl_vat` decimal(21,9) not null default 0, MODIFY `invoice_amount` decimal(21,9) not null default 0
2025-06-19 17:54:09,792 WARNING database DDL Query made to DB:
ALTER TABLE `tabEFD Z Report Invoice` DROP INDEX `parent`
2025-06-19 17:54:09,956 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Insurance Company Detail` DROP INDEX `parent`
2025-06-19 17:54:10,201 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Exchange Item Details` DROP INDEX `parent`
2025-06-19 17:54:10,349 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Additional Costs` MODIFY `cost_per_unit` decimal(21,9) not null default 0
2025-06-19 17:54:10,384 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Additional Costs` DROP INDEX `parent`
2025-06-19 17:54:10,544 WARNING database DDL Query made to DB:
ALTER TABLE `tabSpecial Closing Balance Detail` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `item_balance` decimal(21,9) not null default 0
2025-06-19 17:54:10,581 WARNING database DDL Query made to DB:
ALTER TABLE `tabSpecial Closing Balance Detail` DROP INDEX `parent`
2025-06-19 17:54:10,747 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-19 17:54:10,775 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` DROP INDEX `parent`
2025-06-19 17:54:10,941 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Insurance Vehicle Detail` DROP INDEX `parent`
2025-06-19 17:54:11,055 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile Attachment` ADD COLUMN `parent` varchar(140), ADD COLUMN `parentfield` varchar(140), ADD COLUMN `parenttype` varchar(140)
2025-06-19 17:54:11,210 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Clearance Pro Detail` MODIFY `flt_amount` decimal(21,9) not null default 0
2025-06-19 17:54:11,238 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Clearance Pro Detail` DROP INDEX `parent`
2025-06-19 17:54:11,390 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocument Attachment` DROP INDEX `parent`
2025-06-19 17:54:11,513 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Stock Transfer Details` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0
2025-06-19 17:54:11,555 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Stock Transfer Details` DROP INDEX `parent`
2025-06-19 17:54:11,749 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Invoice` MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-19 17:54:11,777 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Invoice` DROP INDEX `parent`
2025-06-19 17:54:11,930 WARNING database DDL Query made to DB:
ALTER TABLE `tabSQL Process Detail` DROP INDEX `parent`
2025-06-19 17:54:12,058 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Payment` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0
2025-06-19 17:54:12,088 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Payment` DROP INDEX `parent`
2025-06-19 17:54:12,257 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepack Template Detail` MODIFY `qty` decimal(21,9) not null default 0
2025-06-19 17:54:12,294 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepack Template Detail` DROP INDEX `parent`
2025-06-19 17:54:12,473 WARNING database DDL Query made to DB:
ALTER TABLE `tabInv ERR Detail` MODIFY `invoice_amount` decimal(21,9) not null default 0, MODIFY `invoice_exchange_rate` decimal(21,9) not null default 0, MODIFY `invoice_gain_or_loss` decimal(21,9) not null default 0
2025-06-19 17:54:12,512 WARNING database DDL Query made to DB:
ALTER TABLE `tabInv ERR Detail` DROP INDEX `parent`
2025-06-19 17:54:12,730 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Insurance Policy Holder Detail` DROP INDEX `parent`
2025-06-19 17:54:12,913 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Material Request Details` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-19 17:54:12,951 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Material Request Details` DROP INDEX `parent`
2025-06-19 17:54:13,115 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill Items` MODIFY `billitemamount` decimal(21,9) not null default 0, MODIFY `billitemeqvamount` decimal(21,9) not null default 0, MODIFY `billitemmiscamount` decimal(21,9) not null default 0
2025-06-19 17:54:13,156 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill Items` DROP INDEX `parent`
2025-06-19 17:54:13,327 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Consignment Detail` DROP INDEX `parent`
2025-06-19 17:54:13,469 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Exchange Non Stock Item Details` DROP INDEX `parent`
2025-06-19 17:54:13,615 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Salary Component Limit` MODIFY `limit_amount` decimal(21,9) not null default 0
2025-06-19 17:54:13,656 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Salary Component Limit` DROP INDEX `parent`
2025-06-19 17:54:13,824 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Bank Charges Detail` MODIFY `debit_amount` decimal(21,9) not null default 0
2025-06-19 17:54:13,852 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Bank Charges Detail` DROP INDEX `parent`
2025-06-19 17:54:13,976 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-19 17:54:14,016 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` DROP INDEX `parent`
2025-06-19 17:54:14,142 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Consignment Detail` DROP INDEX `parent`
2025-06-19 17:54:14,256 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA TAX Inv Item` MODIFY `amount` decimal(21,9) not null default 0
2025-06-19 17:54:14,284 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA TAX Inv Item` DROP INDEX `parent`
2025-06-19 17:54:14,422 WARNING database DDL Query made to DB:
ALTER TABLE `tabReporting Currency Settings Rate` DROP INDEX `parent`
2025-06-19 17:54:14,556 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill Details` DROP INDEX `parent`
2025-06-19 17:54:14,708 WARNING database DDL Query made to DB:
ALTER TABLE `tabPossible Root Cause` DROP INDEX `parent`
2025-06-19 17:54:14,912 WARNING database DDL Query made to DB:
ALTER TABLE `tabMachine Strip Request Item` MODIFY `qty` decimal(21,9) not null default 0
2025-06-19 17:54:14,939 WARNING database DDL Query made to DB:
ALTER TABLE `tabMachine Strip Request Item` DROP INDEX `parent`
2025-06-19 17:54:15,202 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Details` MODIFY `request_amount` decimal(21,9) not null default 0
2025-06-19 17:54:15,231 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Details` DROP INDEX `parent`
2025-06-19 17:54:15,557 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Accounts Table` MODIFY `request_amount` decimal(21,9) not null default 0, MODIFY `payable_account_currency` varchar(140)
2025-06-19 17:54:15,597 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Accounts Table` DROP INDEX `parent`
2025-06-19 17:54:15,798 WARNING database DDL Query made to DB:
ALTER TABLE `tabPre Delivery Inspection Reading` DROP INDEX `parent`
2025-06-19 17:54:15,954 WARNING database DDL Query made to DB:
ALTER TABLE `tabReference Payment Table` MODIFY `amount` decimal(21,9) not null default 0
2025-06-19 17:54:15,982 WARNING database DDL Query made to DB:
ALTER TABLE `tabReference Payment Table` DROP INDEX `parent`
2025-06-19 17:54:16,143 WARNING database DDL Query made to DB:
ALTER TABLE `tabPre Delivery Inspection Details` DROP INDEX `parent`
2025-06-19 17:54:16,302 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond Reference Table` MODIFY `bond_value` decimal(21,9) not null default 0, MODIFY `weight` decimal(21,9) not null default 0
2025-06-19 17:54:16,342 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond Reference Table` DROP INDEX `parent`
2025-06-19 17:54:16,474 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Issue Detail` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-06-19 17:54:16,513 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Issue Detail` DROP INDEX `parent`
2025-06-19 17:54:16,692 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Detail` DROP INDEX `parent`
2025-06-19 17:54:16,820 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequired Permit` DROP INDEX `parent`
2025-06-19 17:54:16,950 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpenses` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-19 17:54:16,977 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpenses` DROP INDEX `parent`
2025-06-19 17:54:17,158 WARNING database DDL Query made to DB:
ALTER TABLE `tabBorder Procedure Table` DROP INDEX `parent`
2025-06-19 17:54:17,296 WARNING database DDL Query made to DB:
ALTER TABLE `tabImport Border Procedure Table` DROP INDEX `parent`
2025-06-19 17:54:17,449 WARNING database DDL Query made to DB:
ALTER TABLE `tabReporting Status Table` DROP INDEX `parent`
2025-06-19 17:54:17,591 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo Details` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `tare_weight` decimal(21,9) not null default 0, MODIFY `bond_value` decimal(21,9) not null default 0
2025-06-19 17:54:17,632 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo Details` DROP INDEX `parent`
2025-06-19 17:54:17,881 WARNING database DDL Query made to DB:
ALTER TABLE `tabBorder Processing Vehicle Details` DROP INDEX `parent`
2025-06-19 17:54:18,014 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer File Closing Information` DROP INDEX `parent`
2025-06-19 17:54:18,136 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacking List` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-06-19 17:54:18,181 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacking List` DROP INDEX `parent`
2025-06-19 17:54:18,358 WARNING database DDL Query made to DB:
ALTER TABLE `tabPermits Table` DROP INDEX `parent`
2025-06-19 17:54:18,512 WARNING database DDL Query made to DB:
ALTER TABLE `tabMandatory Attachment Table` DROP INDEX `parent`
2025-06-19 17:54:18,661 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond History Table` DROP INDEX `parent`
2025-06-19 17:54:18,797 WARNING database DDL Query made to DB:
ALTER TABLE `tabTire Details` DROP INDEX `parent`
2025-06-19 17:54:18,917 WARNING database DDL Query made to DB:
ALTER TABLE `tabEngine Details` DROP INDEX `parent`
2025-06-19 17:54:19,031 WARNING database DDL Query made to DB:
ALTER TABLE `tabEquipment Table` DROP INDEX `parent`
2025-06-19 17:54:19,159 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Trip Location Update` DROP INDEX `parent`
2025-06-19 17:54:19,277 WARNING database DDL Query made to DB:
ALTER TABLE `tabPower Train Checklist` DROP INDEX `parent`
2025-06-19 17:54:19,395 WARNING database DDL Query made to DB:
ALTER TABLE `tabAir System Checklist` DROP INDEX `parent`
2025-06-19 17:54:19,563 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-19 17:54:19,682 WARNING database DDL Query made to DB:
ALTER TABLE `tabBrake System Details` DROP INDEX `parent`
2025-06-19 17:54:19,815 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel Request Table` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `cost_per_litre` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0
2025-06-19 17:54:19,854 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel Request Table` DROP INDEX `parent`
2025-06-19 17:54:20,013 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel System Details` DROP INDEX `parent`
2025-06-19 17:54:20,125 WARNING database DDL Query made to DB:
ALTER TABLE `tabSuspension Details` DROP INDEX `parent`
2025-06-19 17:54:20,245 WARNING database DDL Query made to DB:
ALTER TABLE `tabElectronics Checklist` DROP INDEX `parent`
2025-06-19 17:54:20,392 WARNING database DDL Query made to DB:
ALTER TABLE `tabTrip Steps Table` MODIFY `distance` decimal(21,9) not null default 0
2025-06-19 17:54:20,431 WARNING database DDL Query made to DB:
ALTER TABLE `tabTrip Steps Table` DROP INDEX `parent`
2025-06-19 17:54:20,620 WARNING database DDL Query made to DB:
ALTER TABLE `tabBrake Checklist` DROP INDEX `parent`
2025-06-19 17:54:20,790 WARNING database DDL Query made to DB:
ALTER TABLE `tabEngine Checklist` DROP INDEX `parent`
2025-06-19 17:54:20,921 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel System Checklist` DROP INDEX `parent`
2025-06-19 17:54:21,043 WARNING database DDL Query made to DB:
ALTER TABLE `tabLighting Checklist` DROP INDEX `parent`
2025-06-19 17:54:21,213 WARNING database DDL Query made to DB:
ALTER TABLE `tabLighting Checklist Details` DROP INDEX `parent`
2025-06-19 17:54:21,399 WARNING database DDL Query made to DB:
ALTER TABLE `tabAir System Details` DROP INDEX `parent`
2025-06-19 17:54:21,539 WARNING database DDL Query made to DB:
ALTER TABLE `tabTire Checklist` DROP INDEX `parent`
2025-06-19 17:54:21,658 WARNING database DDL Query made to DB:
ALTER TABLE `tabSuspension Checklist` DROP INDEX `parent`
2025-06-19 17:54:21,783 WARNING database DDL Query made to DB:
ALTER TABLE `tabSteering Checklist` DROP INDEX `parent`
2025-06-19 17:54:21,939 WARNING database DDL Query made to DB:
ALTER TABLE `tabPower Train Details` DROP INDEX `parent`
2025-06-19 17:54:22,053 WARNING database DDL Query made to DB:
ALTER TABLE `tabSteering Details` DROP INDEX `parent`
2025-06-19 17:54:22,185 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Documents` DROP INDEX `parent`
2025-06-19 17:54:22,415 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransport Assignment` ADD COLUMN `parent` varchar(140), ADD COLUMN `parentfield` varchar(140), ADD COLUMN `parenttype` varchar(140)
2025-06-19 17:54:22,439 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransport Assignment` MODIFY `amount` decimal(21,9) not null default 0
2025-06-19 17:54:22,610 WARNING database DDL Query made to DB:
ALTER TABLE `tabTires Checklist` DROP INDEX `parent`
2025-06-19 17:54:22,744 WARNING database DDL Query made to DB:
ALTER TABLE `tabElectrical Checklist` DROP INDEX `parent`
2025-06-19 17:54:22,896 WARNING database DDL Query made to DB:
ALTER TABLE `tabFixed Expense Table` MODIFY `amount` decimal(21,9) not null default 0
2025-06-19 17:54:22,934 WARNING database DDL Query made to DB:
ALTER TABLE `tabFixed Expense Table` DROP INDEX `parent`
2025-06-19 17:54:23,117 WARNING database DDL Query made to DB:
ALTER TABLE `tabRoute Steps Table` DROP INDEX `parent`
2025-06-19 17:54:23,272 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-19 17:54:23,303 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` DROP INDEX `parent`
2025-06-19 17:54:23,474 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubtrips Table` DROP INDEX `parent`
2025-06-19 17:54:23,633 WARNING database DDL Query made to DB:
ALTER TABLE `tabElectrical Details` DROP INDEX `parent`
2025-06-19 17:54:23,763 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Checklist` DROP INDEX `parent`
2025-06-19 17:54:23,878 WARNING database DDL Query made to DB:
ALTER TABLE `tabTires Details` DROP INDEX `parent`
2025-06-19 17:54:24,001 WARNING database DDL Query made to DB:
ALTER TABLE `tabElectronics Details` DROP INDEX `parent`
2025-06-19 17:54:24,159 WARNING database DDL Query made to DB:
ALTER TABLE `tabOrder Tracking Container` DROP INDEX `parent`
2025-06-19 17:54:24,282 WARNING database DDL Query made to DB:
ALTER TABLE `tabBin List` DROP INDEX `parent`
2025-06-19 17:54:24,496 WARNING database DDL Query made to DB:
ALTER TABLE `tabProducts of Interest` MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0
2025-06-19 17:54:24,523 WARNING database DDL Query made to DB:
ALTER TABLE `tabProducts of Interest` DROP INDEX `parent`
2025-06-19 17:54:24,649 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer Item` DROP INDEX `parent`
2025-06-19 17:54:24,764 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Plan` MODIFY `planned_amount` decimal(21,9) not null default 0, MODIFY `actual_amount` decimal(21,9) not null default 0
2025-06-19 17:54:24,803 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Plan` DROP INDEX `parent`
2025-06-19 17:54:24,968 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssued Items Table` MODIFY `difference` decimal(21,9) not null default 0, MODIFY `requested` decimal(21,9) not null default 0, MODIFY `issued` decimal(21,9) not null default 0
2025-06-19 17:54:24,994 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssued Items Table` DROP INDEX `parent`
2025-06-19 17:54:25,110 WARNING database DDL Query made to DB:
ALTER TABLE `tabUsed Items Table` MODIFY `quantity` decimal(21,9) not null default 0
2025-06-19 17:54:25,137 WARNING database DDL Query made to DB:
ALTER TABLE `tabUsed Items Table` DROP INDEX `parent`
2025-06-19 17:54:25,260 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Items Table` MODIFY `quantity` decimal(21,9) not null default 0
2025-06-19 17:54:25,295 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Items Table` DROP INDEX `parent`
2025-06-19 17:54:25,424 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkshop Services Table` MODIFY `rate_per_hour` decimal(21,9) not null default 0, MODIFY `billable_hours` decimal(21,9) not null default 0
2025-06-19 17:54:25,461 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkshop Services Table` DROP INDEX `parent`
2025-06-19 17:54:25,767 WARNING database DDL Query made to DB:
ALTER TABLE `tabStanbic Payments Info` MODIFY `transfer_amount` decimal(21,9) not null default 0
2025-06-19 17:54:25,836 WARNING database DDL Query made to DB:
ALTER TABLE `tabStanbic Payments Info` DROP INDEX `parent`
2025-06-19 17:54:26,029 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-19 17:54:26,173 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-19 17:54:27,194 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `last_transaction_amount` decimal(21,9) not null default 0
2025-06-19 17:54:29,863 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-21 12:05:50,776 WARNING database DDL Query made to DB:
ALTER TABLE `tabSustainability Metric` ADD COLUMN `metric_name` varchar(140), ADD COLUMN `esg_pillar` varchar(140), ADD COLUMN `gri_disclosure` varchar(140), ADD COLUMN `sdg_link` varchar(140), ADD COLUMN `sdg_target` varchar(140), ADD COLUMN `measures` varchar(140)
2025-06-21 12:06:23,433 WARNING database DDL Query made to DB:
ALTER TABLE `tabSustainability Metric` ADD UNIQUE INDEX IF NOT EXISTS metric_name (`metric_name`)
2025-06-23 13:20:06,598 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-23 13:20:07,574 WARNING database DDL Query made to DB:
ALTER TABLE `tabNumber Card` ADD COLUMN `background_color` varchar(140)
2025-06-23 13:20:08,545 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-23 13:20:09,579 WARNING database DDL Query made to DB:
create table `tabPegged Currency Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_currency` varchar(140),
`pegged_against` varchar(140),
`pegged_exchange_rate` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-23 13:20:10,412 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-23 13:20:10,774 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0
2025-06-23 13:20:12,210 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing File` ADD COLUMN `total_charges` decimal(21,9) not null default 0
2025-06-23 13:20:12,616 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-23 13:20:12,821 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` MODIFY `claimed_amount` decimal(21,9) not null default 0, MODIFY `pending_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `advance_amount` decimal(21,9) not null default 0
2025-06-23 13:20:13,209 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-23 13:20:13,578 WARNING database DDL Query made to DB:
ALTER TABLE `tabRoot Cause Prevention Strategy` MODIFY `estimated_cost` decimal(21,9) not null default 0
2025-06-23 13:20:13,622 WARNING database DDL Query made to DB:
ALTER TABLE `tabRoot Cause Prevention Strategy` DROP INDEX `parent`
2025-06-23 13:20:13,826 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrice Change Request Detail` MODIFY `cost` decimal(21,9) not null default 0, MODIFY `old_price` decimal(21,9) not null default 0, MODIFY `new_price` decimal(21,9) not null default 0
2025-06-23 13:20:13,871 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrice Change Request Detail` DROP INDEX `parent`
2025-06-23 13:20:14,018 WARNING database DDL Query made to DB:
ALTER TABLE `tabEFD Z Report Invoice` MODIFY `amt_ex__sr` decimal(21,9) not null default 0, MODIFY `invoice_amount` decimal(21,9) not null default 0, MODIFY `vat` decimal(21,9) not null default 0, MODIFY `amt_excl_vat` decimal(21,9) not null default 0
2025-06-23 13:20:14,060 WARNING database DDL Query made to DB:
ALTER TABLE `tabEFD Z Report Invoice` DROP INDEX `parent`
2025-06-23 13:20:14,301 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Insurance Company Detail` DROP INDEX `parent`
2025-06-23 13:20:14,510 WARNING database DDL Query made to DB:
ALTER TABLE `tabStation Members` DROP INDEX `parent`
2025-06-23 13:20:14,841 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Exchange Item Details` DROP INDEX `parent`
2025-06-23 13:20:15,061 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Piecework Additional Salary` MODIFY `amount` decimal(21,9) not null default 0
2025-06-23 13:20:15,099 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Piecework Additional Salary` DROP INDEX `parent`
2025-06-23 13:20:15,232 WARNING database DDL Query made to DB:
ALTER TABLE `tabPiecework Payment Allocation` MODIFY `amount` decimal(21,9) not null default 0
2025-06-23 13:20:15,280 WARNING database DDL Query made to DB:
ALTER TABLE `tabPiecework Payment Allocation` DROP INDEX `parent`
2025-06-23 13:20:15,443 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Additional Costs` MODIFY `cost_per_unit` decimal(21,9) not null default 0
2025-06-23 13:20:15,484 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Additional Costs` DROP INDEX `parent`
2025-06-23 13:20:15,671 WARNING database DDL Query made to DB:
ALTER TABLE `tabSpecial Closing Balance Detail` MODIFY `item_balance` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-23 13:20:15,715 WARNING database DDL Query made to DB:
ALTER TABLE `tabSpecial Closing Balance Detail` DROP INDEX `parent`
2025-06-23 13:20:15,920 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-23 13:20:15,965 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` DROP INDEX `parent`
2025-06-23 13:20:16,158 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Insurance Vehicle Detail` DROP INDEX `parent`
2025-06-23 13:20:16,301 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile Attachment` DROP INDEX `parent`
2025-06-23 13:20:16,443 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Clearance Pro Detail` MODIFY `flt_amount` decimal(21,9) not null default 0
2025-06-23 13:20:16,484 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Clearance Pro Detail` DROP INDEX `parent`
2025-06-23 13:20:16,702 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocument Attachment` DROP INDEX `parent`
2025-06-23 13:20:16,896 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Stock Transfer Details` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-23 13:20:16,942 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Stock Transfer Details` DROP INDEX `parent`
2025-06-23 13:20:17,138 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Invoice` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0
2025-06-23 13:20:17,179 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Invoice` DROP INDEX `parent`
2025-06-23 13:20:17,341 WARNING database DDL Query made to DB:
ALTER TABLE `tabSQL Process Detail` DROP INDEX `parent`
2025-06-23 13:20:17,529 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Payment` MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0
2025-06-23 13:20:17,573 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Payment` DROP INDEX `parent`
2025-06-23 13:20:17,764 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepack Template Detail` MODIFY `qty` decimal(21,9) not null default 0
2025-06-23 13:20:17,809 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepack Template Detail` DROP INDEX `parent`
2025-06-23 13:20:17,970 WARNING database DDL Query made to DB:
ALTER TABLE `tabInv ERR Detail` MODIFY `invoice_gain_or_loss` decimal(21,9) not null default 0, MODIFY `invoice_exchange_rate` decimal(21,9) not null default 0, MODIFY `invoice_amount` decimal(21,9) not null default 0
2025-06-23 13:20:18,012 WARNING database DDL Query made to DB:
ALTER TABLE `tabInv ERR Detail` DROP INDEX `parent`
2025-06-23 13:20:18,214 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Insurance Policy Holder Detail` DROP INDEX `parent`
2025-06-23 13:20:18,395 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Material Request Details` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-23 13:20:18,434 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Material Request Details` DROP INDEX `parent`
2025-06-23 13:20:18,611 WARNING database DDL Query made to DB:
ALTER TABLE `tabSingle Piecework Employees` MODIFY `task_rate` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-23 13:20:18,654 WARNING database DDL Query made to DB:
ALTER TABLE `tabSingle Piecework Employees` DROP INDEX `parent`
2025-06-23 13:20:18,808 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill Items` MODIFY `billitemmiscamount` decimal(21,9) not null default 0, MODIFY `billitemamount` decimal(21,9) not null default 0, MODIFY `billitemeqvamount` decimal(21,9) not null default 0
2025-06-23 13:20:18,848 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill Items` DROP INDEX `parent`
2025-06-23 13:20:18,992 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Consignment Detail` DROP INDEX `parent`
2025-06-23 13:20:19,137 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Exchange Non Stock Item Details` DROP INDEX `parent`
2025-06-23 13:20:19,372 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Salary Component Limit` MODIFY `limit_amount` decimal(21,9) not null default 0
2025-06-23 13:20:19,400 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Salary Component Limit` DROP INDEX `parent`
2025-06-23 13:20:19,536 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Bank Charges Detail` MODIFY `debit_amount` decimal(21,9) not null default 0
2025-06-23 13:20:19,576 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Bank Charges Detail` DROP INDEX `parent`
2025-06-23 13:20:19,779 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-23 13:20:19,814 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` DROP INDEX `parent`
2025-06-23 13:20:19,953 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Consignment Detail` DROP INDEX `parent`
2025-06-23 13:20:20,073 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA TAX Inv Item` MODIFY `amount` decimal(21,9) not null default 0
2025-06-23 13:20:20,102 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA TAX Inv Item` DROP INDEX `parent`
2025-06-23 13:20:20,235 WARNING database DDL Query made to DB:
ALTER TABLE `tabReporting Currency Settings Rate` DROP INDEX `parent`
2025-06-23 13:20:20,371 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill Details` DROP INDEX `parent`
2025-06-23 13:20:20,520 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Employee Salary Slip` DROP INDEX `parent`
2025-06-23 13:20:20,695 WARNING database DDL Query made to DB:
ALTER TABLE `tabPossible Root Cause` DROP INDEX `parent`
2025-06-23 13:20:20,966 WARNING database DDL Query made to DB:
ALTER TABLE `tabMachine Strip Request Item` MODIFY `qty` decimal(21,9) not null default 0
2025-06-23 13:20:21,007 WARNING database DDL Query made to DB:
ALTER TABLE `tabMachine Strip Request Item` DROP INDEX `parent`
2025-06-23 13:20:21,349 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Details` MODIFY `request_amount` decimal(21,9) not null default 0
2025-06-23 13:20:21,392 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Details` DROP INDEX `parent`
2025-06-23 13:20:21,770 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Accounts Table` MODIFY `payable_account_currency` varchar(140), MODIFY `request_amount` decimal(21,9) not null default 0
2025-06-23 13:20:21,810 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Accounts Table` DROP INDEX `parent`
2025-06-23 13:20:22,034 WARNING database DDL Query made to DB:
ALTER TABLE `tabPre Delivery Inspection Reading` DROP INDEX `parent`
2025-06-23 13:20:22,256 WARNING database DDL Query made to DB:
ALTER TABLE `tabReference Payment Table` MODIFY `amount` decimal(21,9) not null default 0
2025-06-23 13:20:22,304 WARNING database DDL Query made to DB:
ALTER TABLE `tabReference Payment Table` DROP INDEX `parent`
2025-06-23 13:20:22,492 WARNING database DDL Query made to DB:
ALTER TABLE `tabPre Delivery Inspection Details` DROP INDEX `parent`
2025-06-23 13:20:22,621 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond Reference Table` MODIFY `weight` decimal(21,9) not null default 0, MODIFY `bond_value` decimal(21,9) not null default 0
2025-06-23 13:20:22,652 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond Reference Table` DROP INDEX `parent`
2025-06-23 13:20:22,845 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Issue Detail` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-06-23 13:20:22,871 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Issue Detail` DROP INDEX `parent`
2025-06-23 13:20:23,022 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Detail` DROP INDEX `parent`
2025-06-23 13:20:23,149 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequired Permit` DROP INDEX `parent`
2025-06-23 13:20:23,281 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpenses` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-23 13:20:23,310 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpenses` DROP INDEX `parent`
2025-06-23 13:20:23,508 WARNING database DDL Query made to DB:
ALTER TABLE `tabBorder Procedure Table` DROP INDEX `parent`
2025-06-23 13:20:23,660 WARNING database DDL Query made to DB:
ALTER TABLE `tabImport Border Procedure Table` DROP INDEX `parent`
2025-06-23 13:20:23,790 WARNING database DDL Query made to DB:
ALTER TABLE `tabReporting Status Table` DROP INDEX `parent`
2025-06-23 13:20:23,989 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo Details` MODIFY `tare_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `bond_value` decimal(21,9) not null default 0
2025-06-23 13:20:24,027 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo Details` DROP INDEX `parent`
2025-06-23 13:20:24,238 WARNING database DDL Query made to DB:
ALTER TABLE `tabBorder Processing Vehicle Details` DROP INDEX `parent`
2025-06-23 13:20:24,442 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer File Closing Information` DROP INDEX `parent`
2025-06-23 13:20:24,636 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacking List` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-06-23 13:20:24,675 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacking List` DROP INDEX `parent`
2025-06-23 13:20:25,017 WARNING database DDL Query made to DB:
ALTER TABLE `tabPermits Table` DROP INDEX `parent`
2025-06-23 13:20:25,159 WARNING database DDL Query made to DB:
ALTER TABLE `tabMandatory Attachment Table` DROP INDEX `parent`
2025-06-23 13:20:25,306 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond History Table` DROP INDEX `parent`
2025-06-23 13:20:25,511 WARNING database DDL Query made to DB:
ALTER TABLE `tabTire Details` DROP INDEX `parent`
2025-06-23 13:20:25,715 WARNING database DDL Query made to DB:
ALTER TABLE `tabEngine Details` DROP INDEX `parent`
2025-06-23 13:20:25,862 WARNING database DDL Query made to DB:
ALTER TABLE `tabEquipment Table` DROP INDEX `parent`
2025-06-23 13:20:26,090 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Trip Location Update` DROP INDEX `parent`
2025-06-23 13:20:26,269 WARNING database DDL Query made to DB:
ALTER TABLE `tabPower Train Checklist` DROP INDEX `parent`
2025-06-23 13:20:26,470 WARNING database DDL Query made to DB:
ALTER TABLE `tabAir System Checklist` DROP INDEX `parent`
2025-06-23 13:20:26,760 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-23 13:20:26,894 WARNING database DDL Query made to DB:
ALTER TABLE `tabBrake System Details` DROP INDEX `parent`
2025-06-23 13:20:27,039 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel Request Table` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `cost_per_litre` decimal(21,9) not null default 0
2025-06-23 13:20:27,074 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel Request Table` DROP INDEX `parent`
2025-06-23 13:20:27,230 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel System Details` DROP INDEX `parent`
2025-06-23 13:20:27,421 WARNING database DDL Query made to DB:
ALTER TABLE `tabSuspension Details` DROP INDEX `parent`
2025-06-23 13:20:27,551 WARNING database DDL Query made to DB:
ALTER TABLE `tabElectronics Checklist` DROP INDEX `parent`
2025-06-23 13:20:27,700 WARNING database DDL Query made to DB:
ALTER TABLE `tabTrip Steps Table` MODIFY `distance` decimal(21,9) not null default 0
2025-06-23 13:20:27,732 WARNING database DDL Query made to DB:
ALTER TABLE `tabTrip Steps Table` DROP INDEX `parent`
2025-06-23 13:20:27,885 WARNING database DDL Query made to DB:
ALTER TABLE `tabBrake Checklist` DROP INDEX `parent`
2025-06-23 13:20:28,023 WARNING database DDL Query made to DB:
ALTER TABLE `tabEngine Checklist` DROP INDEX `parent`
2025-06-23 13:20:28,145 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel System Checklist` DROP INDEX `parent`
2025-06-23 13:20:28,292 WARNING database DDL Query made to DB:
ALTER TABLE `tabLighting Checklist` DROP INDEX `parent`
2025-06-23 13:20:28,445 WARNING database DDL Query made to DB:
ALTER TABLE `tabLighting Checklist Details` DROP INDEX `parent`
2025-06-23 13:20:28,600 WARNING database DDL Query made to DB:
ALTER TABLE `tabAir System Details` DROP INDEX `parent`
2025-06-23 13:20:28,803 WARNING database DDL Query made to DB:
ALTER TABLE `tabTire Checklist` DROP INDEX `parent`
2025-06-23 13:20:28,930 WARNING database DDL Query made to DB:
ALTER TABLE `tabSuspension Checklist` DROP INDEX `parent`
2025-06-23 13:20:29,070 WARNING database DDL Query made to DB:
ALTER TABLE `tabSteering Checklist` DROP INDEX `parent`
2025-06-23 13:20:29,201 WARNING database DDL Query made to DB:
ALTER TABLE `tabPower Train Details` DROP INDEX `parent`
2025-06-23 13:20:29,359 WARNING database DDL Query made to DB:
ALTER TABLE `tabSteering Details` DROP INDEX `parent`
2025-06-23 13:20:29,507 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Documents` DROP INDEX `parent`
2025-06-23 13:20:29,728 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransport Assignment` MODIFY `amount` decimal(21,9) not null default 0
2025-06-23 13:20:29,766 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransport Assignment` DROP INDEX `parent`
2025-06-23 13:20:29,995 WARNING database DDL Query made to DB:
ALTER TABLE `tabTires Checklist` DROP INDEX `parent`
2025-06-23 13:20:30,123 WARNING database DDL Query made to DB:
ALTER TABLE `tabElectrical Checklist` DROP INDEX `parent`
2025-06-23 13:20:30,246 WARNING database DDL Query made to DB:
ALTER TABLE `tabFixed Expense Table` MODIFY `amount` decimal(21,9) not null default 0
2025-06-23 13:20:30,279 WARNING database DDL Query made to DB:
ALTER TABLE `tabFixed Expense Table` DROP INDEX `parent`
2025-06-23 13:20:30,460 WARNING database DDL Query made to DB:
ALTER TABLE `tabRoute Steps Table` DROP INDEX `parent`
2025-06-23 13:20:30,585 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-23 13:20:30,615 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` DROP INDEX `parent`
2025-06-23 13:20:30,748 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubtrips Table` DROP INDEX `parent`
2025-06-23 13:20:30,871 WARNING database DDL Query made to DB:
ALTER TABLE `tabElectrical Details` DROP INDEX `parent`
2025-06-23 13:20:31,021 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Checklist` DROP INDEX `parent`
2025-06-23 13:20:31,199 WARNING database DDL Query made to DB:
ALTER TABLE `tabTires Details` DROP INDEX `parent`
2025-06-23 13:20:31,327 WARNING database DDL Query made to DB:
ALTER TABLE `tabElectronics Details` DROP INDEX `parent`
2025-06-23 13:20:31,491 WARNING database DDL Query made to DB:
ALTER TABLE `tabOrder Tracking Container` DROP INDEX `parent`
2025-06-23 13:20:31,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabBin List` DROP INDEX `parent`
2025-06-23 13:20:31,883 WARNING database DDL Query made to DB:
ALTER TABLE `tabProducts of Interest` MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0
2025-06-23 13:20:31,930 WARNING database DDL Query made to DB:
ALTER TABLE `tabProducts of Interest` DROP INDEX `parent`
2025-06-23 13:20:32,106 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer Item` DROP INDEX `parent`
2025-06-23 13:20:32,238 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Plan` MODIFY `actual_amount` decimal(21,9) not null default 0, MODIFY `planned_amount` decimal(21,9) not null default 0
2025-06-23 13:20:32,277 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Plan` DROP INDEX `parent`
2025-06-23 13:20:32,434 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssued Items Table` MODIFY `requested` decimal(21,9) not null default 0, MODIFY `difference` decimal(21,9) not null default 0, MODIFY `issued` decimal(21,9) not null default 0
2025-06-23 13:20:32,463 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssued Items Table` DROP INDEX `parent`
2025-06-23 13:20:32,612 WARNING database DDL Query made to DB:
ALTER TABLE `tabUsed Items Table` MODIFY `quantity` decimal(21,9) not null default 0
2025-06-23 13:20:32,643 WARNING database DDL Query made to DB:
ALTER TABLE `tabUsed Items Table` DROP INDEX `parent`
2025-06-23 13:20:32,780 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Items Table` MODIFY `quantity` decimal(21,9) not null default 0
2025-06-23 13:20:32,810 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Items Table` DROP INDEX `parent`
2025-06-23 13:20:32,958 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkshop Services Table` MODIFY `billable_hours` decimal(21,9) not null default 0, MODIFY `rate_per_hour` decimal(21,9) not null default 0
2025-06-23 13:20:32,993 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkshop Services Table` DROP INDEX `parent`
2025-06-23 13:20:33,370 WARNING database DDL Query made to DB:
ALTER TABLE `tabStanbic Payments Info` MODIFY `transfer_amount` decimal(21,9) not null default 0
2025-06-23 13:20:33,398 WARNING database DDL Query made to DB:
ALTER TABLE `tabStanbic Payments Info` DROP INDEX `parent`
2025-06-23 13:20:38,954 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0
2025-06-23 18:35:49,625 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-23 18:35:50,803 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-23 18:35:52,109 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-23 18:35:53,420 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `total_charges_sum` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0
2025-06-23 18:35:53,665 WARNING database DDL Query made to DB:
create table `tabClearing Service Charge Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_type` varchar(140),
`description` varchar(140),
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-23 18:39:36,804 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-23 18:39:37,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-23 18:39:38,803 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-23 18:39:40,140 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Service Charge Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-06-23 18:46:25,504 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-23 18:46:26,382 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-23 18:46:27,412 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-23 18:46:28,895 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-23 18:46:29,218 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-23 18:46:30,382 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-23 18:46:30,615 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-23 18:46:36,422 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0
2025-06-23 18:50:15,692 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-23 18:50:16,561 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-23 18:50:17,645 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-23 18:50:19,340 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-23 18:50:19,606 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-23 18:50:20,823 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-23 18:50:21,119 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
