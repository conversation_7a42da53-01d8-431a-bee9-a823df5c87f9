2025-06-19 17:04:13,142 WARNING database DDL Query made to DB:
create table `tabChangelog Feed` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`app_name` varchar(140),
`link` longtext,
`posting_timestamp` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `posting_timestamp`(`posting_timestamp`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:13,336 WARNING database DDL Query made to DB:
create table `tabTag` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:13,438 WARNING database DDL Query made to DB:
create table `tabConsole Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`script` longtext,
`type` varchar(140),
`committed` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:13,523 WARNING database DDL Query made to DB:
create table `tabCustom HTML Block` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`private` int(1) not null default 0,
`html` longtext,
`script` longtext,
`style` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:13,671 WARNING database DDL Query made to DB:
create table `tabNotification Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`enable_email_notifications` int(1) not null default 1,
`enable_email_mention` int(1) not null default 1,
`enable_email_assignment` int(1) not null default 1,
`enable_email_threads_on_assigned_document` int(1) not null default 1,
`enable_email_energy_point` int(1) not null default 1,
`enable_email_share` int(1) not null default 1,
`enable_email_event_reminders` int(1) not null default 1,
`user` varchar(140),
`seen` int(1) not null default 0,
`energy_points_system_notifications` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:13,734 WARNING database DDL Query made to DB:
create table `tabRoute History` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`route` varchar(140),
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:13,818 WARNING database DDL Query made to DB:
create table `tabNote` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`public` int(1) not null default 0,
`notify_on_login` int(1) not null default 0,
`notify_on_every_login` int(1) not null default 0,
`expire_notification_on` date,
`content` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `expire_notification_on`(`expire_notification_on`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:13,873 WARNING database DDL Query made to DB:
create table `tabDashboard Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`chart_config` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:13,977 WARNING database DDL Query made to DB:
create table `tabKanban Board` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`kanban_board_name` varchar(140) unique,
`reference_doctype` varchar(140),
`field_name` varchar(140),
`private` int(1) not null default 0,
`show_labels` int(1) not null default 0,
`filters` longtext,
`fields` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:14,052 WARNING database DDL Query made to DB:
create table `tabToDo` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140) default 'Open',
`priority` varchar(140) default 'Medium',
`color` varchar(140),
`date` date,
`allocated_to` varchar(140),
`description` longtext,
`reference_type` varchar(140),
`reference_name` varchar(140),
`role` varchar(140),
`assigned_by` varchar(140),
`assigned_by_full_name` varchar(140),
`sender` varchar(140),
`assignment_rule` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:14,094 WARNING database DDL Query made to DB:
ALTER TABLE `tabToDo`
				ADD INDEX IF NOT EXISTS `reference_type_reference_name_index`(reference_type, reference_name)
2025-06-19 17:04:14,142 WARNING database DDL Query made to DB:
create table `tabDashboard Chart Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`y_field` varchar(140),
`color` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:14,234 WARNING database DDL Query made to DB:
create table `tabGlobal Search DocType` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:14,418 WARNING database DDL Query made to DB:
create table `tabNumber Card Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`card` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:14,475 WARNING database DDL Query made to DB:
create table `tabDashboard Chart Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`chart` varchar(140),
`width` varchar(140) default 'Half',
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:14,534 WARNING database DDL Query made to DB:
create table `tabList Filter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`filter_name` varchar(140),
`reference_doctype` varchar(140),
`for_user` varchar(140),
`filters` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:14,588 WARNING database DDL Query made to DB:
create table `tabNote Seen By` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:14,738 WARNING database DDL Query made to DB:
create table `tabEvent Participants` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`email` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:14,888 WARNING database DDL Query made to DB:
create table `tabTag Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`document_name` varchar(140),
`tag` varchar(140),
`title` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:14,953 WARNING database DDL Query made to DB:
ALTER TABLE `tabTag Link`
				ADD INDEX IF NOT EXISTS `document_type_document_name_index`(document_type, document_name)
2025-06-19 17:04:15,028 WARNING database DDL Query made to DB:
create table `tabNotification Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`subject` text,
`for_user` varchar(140),
`type` varchar(140),
`email_content` longtext,
`document_type` varchar(140),
`read` int(1) not null default 0,
`document_name` varchar(140),
`attached_file` longtext,
`from_user` varchar(140),
`link` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `for_user`(`for_user`),
index `document_name`(`document_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:15,156 WARNING database DDL Query made to DB:
create table `tabCalendar View` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`subject_field` varchar(140),
`start_date_field` varchar(140),
`end_date_field` varchar(140),
`all_day` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:15,225 WARNING database DDL Query made to DB:
create table `tabKanban Board Column` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`column_name` varchar(140),
`status` varchar(140) default 'Active',
`indicator` varchar(140) default 'Gray',
`order` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:15,294 WARNING database DDL Query made to DB:
create table `tabNotification Subscribed Document` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:15,383 WARNING database DDL Query made to DB:
create table `tabDesktop Icon` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`module_name` varchar(140),
`label` varchar(140),
`standard` int(1) not null default 0,
`custom` int(1) not null default 0,
`app` varchar(140),
`description` text,
`category` varchar(140),
`hidden` int(1) not null default 0,
`blocked` int(1) not null default 0,
`force_show` int(1) not null default 0,
`type` varchar(140),
`_doctype` varchar(140),
`_report` varchar(140),
`link` text,
`color` varchar(140),
`icon` varchar(140),
`reverse` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:15,437 WARNING database DDL Query made to DB:
alter table `tabDesktop Icon`
					add unique `unique_module_name_owner_standard`(module_name, owner, standard)
2025-06-19 17:04:15,507 WARNING database DDL Query made to DB:
create table `tabList View Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disable_count` int(1) not null default 0,
`disable_comment_count` int(1) not null default 0,
`disable_sidebar_stats` int(1) not null default 0,
`disable_auto_refresh` int(1) not null default 0,
`allow_edit` int(1) not null default 0,
`disable_automatic_recency_filters` int(1) not null default 0,
`total_fields` varchar(140),
`fields` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:15,617 WARNING database DDL Query made to DB:
create table `tabEvent` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`subject` text,
`event_category` varchar(140),
`event_type` varchar(140),
`color` varchar(140),
`send_reminder` int(1) not null default 1,
`repeat_this_event` int(1) not null default 0,
`starts_on` datetime(6),
`ends_on` datetime(6),
`status` varchar(140) default 'Open',
`sender` varchar(140),
`all_day` int(1) not null default 0,
`sync_with_google_calendar` int(1) not null default 0,
`add_video_conferencing` int(1) not null default 0,
`google_calendar` varchar(140),
`google_calendar_id` varchar(140),
`google_calendar_event_id` varchar(320),
`google_meet_link` text,
`pulled_from_google_calendar` int(1) not null default 0,
`repeat_on` varchar(140),
`repeat_till` date,
`monday` int(1) not null default 0,
`tuesday` int(1) not null default 0,
`wednesday` int(1) not null default 0,
`thursday` int(1) not null default 0,
`friday` int(1) not null default 0,
`saturday` int(1) not null default 0,
`sunday` int(1) not null default 0,
`description` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `event_type`(`event_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:15,876 WARNING database DDL Query made to DB:
create table `tabWebhook Header` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`key` text,
`value` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:16,097 WARNING database DDL Query made to DB:
create table `tabWebhook` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`webhook_doctype` varchar(140),
`webhook_docevent` varchar(140),
`enabled` int(1) not null default 1,
`condition` text,
`request_url` text,
`is_dynamic_url` int(1) not null default 0,
`timeout` int(11) not null default 5,
`background_jobs_queue` varchar(140),
`request_method` varchar(140) default 'POST',
`request_structure` varchar(140),
`enable_security` int(1) not null default 0,
`webhook_secret` text,
`webhook_json` longtext,
`preview_document` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:16,183 WARNING database DDL Query made to DB:
create table `tabOAuth Client` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`client_id` varchar(140),
`app_name` varchar(140),
`user` varchar(140),
`client_secret` varchar(140),
`skip_authorization` int(1) not null default 0,
`scopes` text default 'all openid',
`redirect_uris` text,
`default_redirect_uri` varchar(140),
`grant_type` varchar(140),
`response_type` varchar(140) default 'Code',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:16,643 WARNING database DDL Query made to DB:
create table `tabLDAP Group Mapping` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ldap_group` varchar(140),
`erpnext_role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:16,741 WARNING database DDL Query made to DB:
create table `tabSlack Webhook URL` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`webhook_name` varchar(140) unique,
`webhook_url` varchar(140),
`show_document_link` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:16,864 WARNING database DDL Query made to DB:
create table `tabOAuth Authorization Code` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`client` varchar(140),
`user` varchar(140),
`scopes` text,
`authorization_code` varchar(140) unique,
`expiration_time` datetime(6),
`redirect_uri_bound_to_authorization_code` varchar(140),
`validity` varchar(140),
`nonce` varchar(140),
`code_challenge` varchar(140),
`code_challenge_method` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:17,092 WARNING database DDL Query made to DB:
create table `tabOAuth Bearer Token` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`client` varchar(140),
`user` varchar(140),
`scopes` text,
`access_token` varchar(140) unique,
`refresh_token` varchar(140),
`expiration_time` datetime(6),
`expires_in` int(11) not null default 0,
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:17,181 WARNING database DDL Query made to DB:
create table `tabSocial Login Key` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enable_social_login` int(1) not null default 0,
`social_login_provider` varchar(140) default 'Custom',
`client_id` varchar(140),
`provider_name` varchar(140),
`client_secret` text,
`icon` varchar(140),
`base_url` varchar(140),
`sign_ups` varchar(140),
`authorize_url` varchar(140),
`access_token_url` varchar(140),
`redirect_url` varchar(140),
`api_endpoint` varchar(140),
`custom_base_url` int(1) not null default 0,
`api_endpoint_args` longtext,
`auth_url_data` longtext,
`user_id_property` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:17,493 WARNING database DDL Query made to DB:
create table `tabGoogle Calendar` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enable` int(1) not null default 1,
`calendar_name` varchar(140) unique,
`user` varchar(140),
`pull_from_google_calendar` int(1) not null default 1,
`sync_as_public` int(1) not null default 0,
`push_to_google_calendar` int(1) not null default 1,
`google_calendar_id` varchar(140),
`refresh_token` text,
`authorization_code` text,
`next_sync_token` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:17,762 WARNING database DDL Query made to DB:
create table `tabConnected App` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`provider_name` varchar(140),
`openid_configuration` varchar(140),
`client_id` varchar(140),
`redirect_uri` varchar(140),
`client_secret` text,
`authorization_uri` text,
`token_uri` varchar(140),
`revocation_uri` varchar(140),
`userinfo_uri` varchar(140),
`introspection_uri` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:17,852 WARNING database DDL Query made to DB:
create table `tabQuery Parameters` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`key` varchar(140),
`value` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:17,925 WARNING database DDL Query made to DB:
create table `tabGoogle Contacts` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enable` int(1) not null default 0,
`email_id` varchar(140),
`last_sync_on` datetime(6),
`authorization_code` text,
`refresh_token` text,
`next_sync_token` text,
`pull_from_google_contacts` int(1) not null default 0,
`push_to_google_contacts` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:17,996 WARNING database DDL Query made to DB:
create table `tabWebhook Request Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`webhook` varchar(140),
`reference_document` varchar(140),
`headers` longtext,
`data` longtext,
`user` varchar(140),
`url` text,
`response` longtext,
`error` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:18,076 WARNING database DDL Query made to DB:
create table `tabOAuth Client Role` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:18,135 WARNING database DDL Query made to DB:
create table `tabOAuth Scope` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`scope` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:18,207 WARNING database DDL Query made to DB:
create table `tabIntegration Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`request_id` varchar(140),
`integration_request_service` varchar(140),
`is_remote_request` int(1) not null default 0,
`request_description` varchar(140),
`status` varchar(140) default 'Queued',
`url` text,
`request_headers` longtext,
`data` longtext,
`output` longtext,
`error` longtext,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:18,277 WARNING database DDL Query made to DB:
create table `tabToken Cache` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`connected_app` varchar(140),
`provider_name` varchar(140),
`access_token` text,
`refresh_token` text,
`expires_in` int(11) not null default 0,
`state` varchar(140),
`success_uri` varchar(140),
`token_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:18,345 WARNING database DDL Query made to DB:
create table `tabWebhook Data` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fieldname` varchar(140),
`key` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:18,463 WARNING database DDL Query made to DB:
create table `tabPrint Heading` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`print_heading` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:18,700 WARNING database DDL Query made to DB:
create table `tabNetwork Printer Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`server_ip` varchar(140) default 'localhost',
`port` int(11) not null default 631,
`printer_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:18,819 WARNING database DDL Query made to DB:
create table `tabLetter Head` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`letter_head_name` varchar(140) unique,
`source` varchar(140),
`footer_source` varchar(140) default 'HTML',
`disabled` int(1) not null default 0,
`is_default` int(1) not null default 0,
`image` text,
`image_height` decimal(21,9) not null default 0,
`image_width` decimal(21,9) not null default 0,
`align` varchar(140) default 'Left',
`content` longtext,
`footer` longtext,
`footer_image` text,
`footer_image_height` decimal(21,9) not null default 0,
`footer_image_width` decimal(21,9) not null default 0,
`footer_align` varchar(140),
`header_script` longtext,
`footer_script` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `is_default`(`is_default`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:18,894 WARNING database DDL Query made to DB:
create table `tabPrint Format Field Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`field` varchar(140),
`template_file` varchar(140),
`module` varchar(140),
`standard` int(1) not null default 0,
`template` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:19,243 WARNING database DDL Query made to DB:
create table `tabAddress Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`country` varchar(140) unique,
`is_default` int(1) not null default 0,
`template` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:19,563 WARNING database DDL Query made to DB:
create table `tabAddress` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`address_title` varchar(140),
`address_type` varchar(140),
`address_line1` varchar(240),
`address_line2` varchar(240),
`city` varchar(140),
`county` varchar(140),
`state` varchar(140),
`country` varchar(140),
`pincode` varchar(140),
`email_id` varchar(140),
`phone` varchar(140),
`fax` varchar(140),
`is_primary_address` int(1) not null default 0,
`is_shipping_address` int(1) not null default 0,
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `city`(`city`),
index `country`(`country`),
index `pincode`(`pincode`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:19,666 WARNING database DDL Query made to DB:
create table `tabContact Phone` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`phone` varchar(140),
`is_primary_phone` int(1) not null default 0,
`is_primary_mobile_no` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:19,733 WARNING database DDL Query made to DB:
create table `tabSalutation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salutation` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:19,798 WARNING database DDL Query made to DB:
create table `tabGender` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`gender` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:19,913 WARNING database DDL Query made to DB:
create table `tabContact` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`full_name` varchar(140),
`email_id` varchar(140),
`user` varchar(140),
`address` varchar(140),
`sync_with_google_contacts` int(1) not null default 0,
`status` varchar(140) default 'Passive',
`salutation` varchar(140),
`designation` varchar(140),
`gender` varchar(140),
`phone` varchar(140),
`mobile_no` varchar(140),
`company_name` varchar(140),
`image` text,
`google_contacts` varchar(140),
`google_contacts_id` varchar(140),
`pulled_from_google_contacts` int(1) not null default 0,
`is_primary_contact` int(1) not null default 0,
`department` varchar(140),
`unsubscribed` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `email_id`(`email_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:20,014 WARNING database DDL Query made to DB:
create table `tabContact Email` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email_id` varchar(140),
`is_primary` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:20,163 WARNING database DDL Query made to DB:
create table `tabEnergy Point Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`rule_name` varchar(140) unique,
`reference_doctype` varchar(140),
`for_doc_event` varchar(140) default 'Custom',
`field_to_check` varchar(140),
`points` int(11) not null default 0,
`for_assigned_users` int(1) not null default 0,
`user_field` varchar(140),
`multiplier_field` varchar(140),
`max_points` int(11) not null default 0,
`apply_only_once` int(1) not null default 0,
`condition` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:20,386 WARNING database DDL Query made to DB:
create table `tabEnergy Point Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`type` varchar(140),
`points` int(11) not null default 0,
`rule` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`reverted` int(1) not null default 0,
`revert_of` varchar(140),
`reason` text,
`seen` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user`(`user`),
index `reference_name`(`reference_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:20,504 WARNING database DDL Query made to DB:
create table `tabReview Level` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`level_name` varchar(140) unique,
`role` varchar(140) unique,
`review_points` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:20,604 WARNING database DDL Query made to DB:
create table `tabMilestone` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_type` varchar(140),
`reference_name` varchar(140),
`track_field` varchar(140),
`value` varchar(140),
`milestone_tracker` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_type`(`reference_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:20,696 WARNING database DDL Query made to DB:
ALTER TABLE `tabMilestone`
				ADD INDEX IF NOT EXISTS `reference_type_reference_name_index`(reference_type, reference_name)
2025-06-19 17:04:20,904 WARNING database DDL Query made to DB:
create table `tabMilestone Tracker` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140) unique,
`track_field` varchar(140),
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:20,977 WARNING database DDL Query made to DB:
create table `tabAssignment Rule User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:21,061 WARNING database DDL Query made to DB:
create table `tabAssignment Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`due_date_based_on` varchar(140),
`priority` int(11) not null default 0,
`disabled` int(1) not null default 0,
`description` text default 'Automatic Assignment',
`assign_condition` longtext,
`unassign_condition` longtext,
`close_condition` longtext,
`rule` varchar(140),
`field` varchar(140),
`last_user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:21,134 WARNING database DDL Query made to DB:
create table `tabReminder` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`remind_at` datetime(6),
`description` text,
`reminder_doctype` varchar(140),
`reminder_docname` varchar(140),
`notified` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user`(`user`),
index `remind_at`(`remind_at`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:21,231 WARNING database DDL Query made to DB:
create table `tabAuto Repeat Day` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`day` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:21,317 WARNING database DDL Query made to DB:
create table `tabAuto Repeat` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`reference_document` varchar(140),
`submit_on_creation` int(1) not null default 0,
`start_date` date,
`end_date` date,
`disabled` int(1) not null default 0,
`frequency` varchar(140),
`repeat_on_day` int(11) not null default 0,
`repeat_on_last_day` int(1) not null default 0,
`next_schedule_date` date,
`notify_by_email` int(1) not null default 0,
`recipients` text,
`template` varchar(140),
`subject` varchar(140),
`message` text default 'Please find attached {{ doc.doctype }} #{{ doc.name }}',
`print_format` varchar(140),
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `next_schedule_date`(`next_schedule_date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:04:21,375 WARNING database DDL Query made to DB:
create table `tabAssignment Rule Day` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`day` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:07:10,860 WARNING database DDL Query made to DB:
DROP USER IF EXISTS '_5028e5ff99f085ba'@'localhost'
2025-06-19 17:07:12,991 WARNING database DDL Query made to DB:
DROP DATABASE IF EXISTS `_5028e5ff99f085ba`
2025-06-19 17:07:12,994 WARNING database DDL Query made to DB:
CREATE USER '_5028e5ff99f085ba'@'localhost' IDENTIFIED BY 'WSV0wkJQIXMBmdrh'
2025-06-19 17:07:12,995 WARNING database DDL Query made to DB:
CREATE DATABASE `_5028e5ff99f085ba` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
2025-06-19 17:08:11,046 WARNING database DDL Query made to DB:
create table if not exists `__Auth` (
				`doctype` VARCHAR(140) NOT NULL,
				`name` VARCHAR(255) NOT NULL,
				`fieldname` VARCHAR(140) NOT NULL,
				`password` TEXT NOT NULL,
				`encrypted` INT(1) NOT NULL DEFAULT 0,
				PRIMARY KEY (`doctype`, `name`, `fieldname`)
			) ENGINE=InnoDB ROW_FORMAT=DYNAMIC CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-06-19 17:08:11,053 WARNING database DDL Query made to DB:
create table if not exists __UserSettings (
			`user` VARCHAR(180) NOT NULL,
			`doctype` VARCHAR(180) NOT NULL,
			`data` TEXT,
			UNIQUE(user, doctype)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-06-19 17:12:28,933 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-19 17:12:30,052 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` ADD COLUMN `protect_attached_files` int(1) not null default 0
2025-06-19 17:12:30,351 WARNING database DDL Query made to DB:
ALTER TABLE `tabNumber Card` ADD COLUMN `background_color` varchar(140)
2025-06-19 17:12:31,714 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow Document State` ADD COLUMN `send_email` int(1) not null default 1
2025-06-19 17:12:32,744 WARNING database DDL Query made to DB:
ALTER TABLE `tabEvent` MODIFY `google_meet_link` text
2025-06-19 17:12:33,040 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-19 17:12:33,420 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcess Statement Of Accounts` ADD COLUMN `categorize_by` varchar(140) default 'Categorize by Voucher (Consolidated)'
2025-06-19 17:12:33,951 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-19 17:12:33,971 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `sales_incoming_rate` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0
2025-06-19 17:12:34,316 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0
2025-06-19 17:12:34,792 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-19 17:12:34,844 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0
2025-06-19 17:12:35,385 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-19 17:12:35,408 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Item` MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0
2025-06-19 17:12:36,352 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` ADD COLUMN `dispatch_address` varchar(140), ADD COLUMN `dispatch_address_display` longtext
2025-06-19 17:12:36,374 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `naming_series` varchar(140) default 'RTS-PINV-.#####', MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0
2025-06-19 17:12:36,835 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD COLUMN `allow_partial_payment` int(1) not null default 0, ADD COLUMN `project` varchar(140)
2025-06-19 17:12:36,871 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:37,086 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Taxes and Charges` MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_tax_amount` decimal(21,9) not null default 0, MODIFY `base_tax_amount_after_discount_amount` decimal(21,9) not null default 0, MODIFY `tax_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `tax_amount_after_discount_amount` decimal(21,9) not null default 0
2025-06-19 17:12:37,378 WARNING database DDL Query made to DB:
create table `tabPegged Currency Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_currency` varchar(140),
`pegged_against` varchar(140),
`pegged_exchange_rate` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-19 17:12:38,424 WARNING database DDL Query made to DB:
ALTER TABLE `tabContract` ADD COLUMN `party_full_name` varchar(140)
2025-06-19 17:12:38,823 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequest for Quotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-19 17:12:39,243 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-19 17:12:39,265 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order Item` MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `blanket_order_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `last_purchase_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `subcontracted_quantity` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0
2025-06-19 17:12:39,675 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-19 17:12:39,700 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation` MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0
2025-06-19 17:12:40,270 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-19 17:12:40,292 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation Item` MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0
2025-06-19 17:12:40,775 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` ADD COLUMN `has_unit_price_items` int(1) not null default 0, ADD COLUMN `dispatch_address` varchar(140), ADD COLUMN `dispatch_address_display` longtext
2025-06-19 17:12:40,800 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0
2025-06-19 17:12:41,553 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-19 17:12:41,575 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `naming_series` varchar(140) default 'RTS-SO-.#####', MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0
2025-06-19 17:12:42,179 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-19 17:12:42,200 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation Item` MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `gross_profit` decimal(21,9) not null default 0, MODIFY `blanket_order_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-19 17:12:42,631 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-19 17:12:42,652 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0
2025-06-19 17:12:43,048 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-19 17:12:43,070 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `production_plan_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `planned_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `blanket_order_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `produced_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `work_order_qty` decimal(21,9) not null default 0, MODIFY `picked_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `gross_profit` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0
2025-06-19 17:12:43,362 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-19 17:12:43,942 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `worker_subsistence` decimal(21,9) not null default 0, MODIFY `other_allowance` decimal(21,9) not null default 0
2025-06-19 17:12:44,539 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0
2025-06-19 17:12:44,845 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Operation` MODIFY `completed_qty` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `batch_size` decimal(21,9) not null default 0, MODIFY `actual_operation_time` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `time_in_mins` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0
2025-06-19 17:12:45,056 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sub Assembly Item` ADD COLUMN `ordered_qty` decimal(21,9) not null default 0
2025-06-19 17:12:45,076 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sub Assembly Item` MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `wo_produced_qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0
2025-06-19 17:12:45,381 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` MODIFY `additional_operating_cost` decimal(21,9) not null default 0, MODIFY `total_operating_cost` decimal(21,9) not null default 0, MODIFY `lead_time` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `corrective_operation_cost` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0
2025-06-19 17:12:46,123 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-19 17:12:46,142 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `installed_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0
2025-06-19 17:12:46,375 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Item Valuation` ADD COLUMN `recreate_stock_ledgers` int(1) not null default 0
2025-06-19 17:12:46,412 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Item Valuation` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:46,682 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `stock_value` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_value_difference` decimal(21,9) not null default 0, MODIFY `outgoing_rate` decimal(21,9) not null default 0, MODIFY `qty_after_transaction` decimal(21,9) not null default 0
2025-06-19 17:12:46,726 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `posting_date_index`, DROP INDEX `item_code`, DROP INDEX `warehouse`
2025-06-19 17:12:46,809 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry`
				ADD INDEX IF NOT EXISTS `item_code_warehouse_posting_datetime_creation_index`(item_code, warehouse, posting_datetime, creation)
2025-06-19 17:12:47,221 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-19 17:12:47,241 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item` MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `amount_difference_with_purchase_invoice` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `received_stock_qty` decimal(21,9) not null default 0, MODIFY `sales_incoming_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0
2025-06-19 17:12:48,032 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt` ADD COLUMN `dispatch_address` varchar(140), ADD COLUMN `dispatch_address_display` longtext
2025-06-19 17:12:48,052 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt` MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `naming_series` varchar(140) default 'RTS-PR-.#####', MODIFY `total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2025-06-19 17:12:48,366 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `per_ordered` decimal(21,9) not null default 0
2025-06-19 17:12:49,038 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Category` ADD COLUMN `non_depreciable_category` int(1) not null default 0
2025-06-19 17:12:49,414 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset` MODIFY `total_asset_cost` decimal(21,9) not null default 0, MODIFY `additional_asset_cost` decimal(21,9) not null default 0, MODIFY `value_after_depreciation` decimal(21,9) not null default 0, MODIFY `opening_accumulated_depreciation` decimal(21,9) not null default 0, MODIFY `purchase_amount` decimal(21,9) not null default 0, MODIFY `gross_purchase_amount` decimal(21,9) not null default 0
2025-06-19 17:12:50,112 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` ADD COLUMN `expense_account` varchar(140), ADD COLUMN `cost_center` varchar(140)
2025-06-19 17:12:50,132 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` MODIFY `required_qty` decimal(21,9) not null default 0, MODIFY `current_stock` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `consumed_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-19 17:12:50,399 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Requisition` MODIFY `expected_compensation` decimal(21,9) not null default 0, MODIFY `time_to_fill` decimal(21,9)
2025-06-19 17:12:50,431 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Requisition` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:50,606 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Boarding Activity` MODIFY `task_weight` decimal(21,9) not null default 0
2025-06-19 17:12:50,637 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Boarding Activity` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:50,798 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer Term` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:51,029 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Cycle` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:51,232 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:51,394 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpected Skill Set` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:51,548 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter content` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:51,703 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List Date` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:51,865 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer Term Template` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:52,043 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:52,204 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Separation Template` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:52,385 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Request` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:52,579 WARNING database DDL Query made to DB:
ALTER TABLE `tabPWA Notification` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:52,747 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Feedback` MODIFY `average_rating` decimal(3,2)
2025-06-19 17:12:52,778 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Feedback` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:52,989 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisee` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:53,183 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Checkin` MODIFY `latitude` decimal(21,9) not null default 0, MODIFY `longitude` decimal(21,9) not null default 0
2025-06-19 17:12:53,218 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Checkin` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:53,434 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-19 17:12:53,464 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:53,687 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` MODIFY `leave_balance` decimal(21,9) not null default 0, MODIFY `encashment_amount` decimal(21,9) not null default 0, MODIFY `encashment_days` decimal(21,9) not null default 0, MODIFY `actual_encashable_days` decimal(21,9) not null default 0
2025-06-19 17:12:53,723 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:53,937 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter Template` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:54,206 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `pending_amount` decimal(21,9) not null default 0, MODIFY `advance_amount` decimal(21,9) not null default 0, MODIFY `claimed_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0
2025-06-19 17:12:54,243 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:54,445 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Assignment` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:54,632 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List Allow` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:54,789 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Asset` MODIFY `actual_cost` decimal(21,9) not null default 0, MODIFY `cost` decimal(21,9) not null default 0
2025-06-19 17:12:54,822 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Asset` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:55,143 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Onboarding Template` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:55,357 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Referral` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:55,551 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Period` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:55,733 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service Item` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:55,900 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Detail` MODIFY `sanctioned_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-19 17:12:55,941 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Detail` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:56,131 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Itinerary` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:56,304 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Feedback` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:56,472 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Training` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:56,618 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal KRA` MODIFY `goal_completion` decimal(21,9) not null default 0, MODIFY `goal_score` decimal(21,9) not null default 0, MODIFY `per_weightage` decimal(21,9) not null default 0
2025-06-19 17:12:56,650 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal KRA` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:56,818 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Outstanding Statement` MODIFY `amount` decimal(21,9) not null default 0
2025-06-19 17:12:56,849 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Outstanding Statement` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:57,043 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoal` MODIFY `progress` decimal(21,9) not null default 0
2025-06-19 17:12:57,075 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoal` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:57,309 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview` MODIFY `expected_average_rating` decimal(3,2), MODIFY `average_rating` decimal(3,2)
2025-06-19 17:12:57,344 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:57,669 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Detail` MODIFY `annual_allocation` decimal(21,9) not null default 0
2025-06-19 17:12:57,705 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Detail` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:57,850 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill Assessment` MODIFY `rating` decimal(3,2)
2025-06-19 17:12:57,880 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill Assessment` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:58,025 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant Source` ADD UNIQUE INDEX IF NOT EXISTS source_name (`source_name`)
2025-06-19 17:12:58,058 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Applicant Source` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:58,225 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterest` ADD UNIQUE INDEX IF NOT EXISTS interest (`interest`)
2025-06-19 17:12:58,255 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterest` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:58,444 WARNING database DDL Query made to DB:
ALTER TABLE `tabKRA` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:58,602 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment Approver` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:58,772 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Criteria` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:58,997 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:59,223 WARNING database DDL Query made to DB:
ALTER TABLE `tabExit Interview` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:59,403 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:59,648 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Opening` MODIFY `upper_range` decimal(21,9) not null default 0, MODIFY `lower_range` decimal(21,9) not null default 0
2025-06-19 17:12:59,680 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Opening` ADD INDEX `creation`(`creation`)
2025-06-19 17:12:59,911 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application` MODIFY `total_leave_days` decimal(21,9) not null default 0, MODIFY `leave_balance` decimal(21,9) not null default 0
2025-06-19 17:12:59,947 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Application` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:00,124 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Round` MODIFY `expected_average_rating` decimal(3,2)
2025-06-19 17:13:00,156 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Round` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:00,327 WARNING database DDL Query made to DB:
ALTER TABLE `tabOffer Term` ADD UNIQUE INDEX IF NOT EXISTS offer_term (`offer_term`)
2025-06-19 17:13:00,358 WARNING database DDL Query made to DB:
ALTER TABLE `tabOffer Term` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:00,648 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim` MODIFY `total_amount_reimbursed` decimal(21,9) not null default 0, MODIFY `total_advance_amount` decimal(21,9) not null default 0, MODIFY `total_claimed_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_sanctioned_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0
2025-06-19 17:13:00,683 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:00,946 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Allocation` MODIFY `new_leaves_allocated` decimal(21,9) not null default 0, MODIFY `total_leaves_encashed` decimal(21,9) not null default 0, MODIFY `carry_forwarded_leaves_count` decimal(21,9) not null default 0, MODIFY `total_leaves_allocated` decimal(21,9) not null default 0, MODIFY `unused_leaves` decimal(21,9) not null default 0
2025-06-19 17:13:00,978 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Allocation` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:01,180 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Onboarding` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:01,351 WARNING database DDL Query made to DB:
ALTER TABLE `tabIdentification Document Type` ADD UNIQUE INDEX IF NOT EXISTS identification_document_type (`identification_document_type`)
2025-06-19 17:13:01,382 WARNING database DDL Query made to DB:
ALTER TABLE `tabIdentification Document Type` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:01,560 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance Request` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:01,743 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Goal` MODIFY `score_earned` decimal(21,9) not null default 0, MODIFY `per_weightage` decimal(21,9) not null default 0, MODIFY `score` decimal(21,9) not null default 0
2025-06-19 17:13:01,774 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Goal` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:02,192 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Grievance` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:02,374 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Type` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:02,555 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Taxes and Charges` MODIFY `tax_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-19 17:13:02,587 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Taxes and Charges` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:02,740 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill` MODIFY `proficiency` decimal(3,2)
2025-06-19 17:13:02,771 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill` ADD INDEX `creation`(`creation`)
2025-06-19 17:13:02,923 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployment Type` ADD INDEX `creation`(`creation`)
