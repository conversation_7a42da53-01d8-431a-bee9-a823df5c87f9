2025-01-23 13:57:13,802 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:17:48,463 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:17:51,636 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:17:53,073 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:17:53,487 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:17:54,915 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:17:55,378 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:17:55,815 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:17:56,297 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:17:56,786 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:17:57,362 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:34:15,255 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:34:17,082 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:34:17,595 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:40:24,671 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:40:25,970 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:40:26,471 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:40:26,803 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:41:14,308 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:41:16,821 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:41:17,503 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:41:17,838 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:41:18,229 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:41:19,193 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 14:41:20,088 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-23 18:16:39,966 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-24 08:40:06,762 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-24 10:51:41,432 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-24 11:20:39,951 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-24 11:47:26,746 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-24 11:48:51,502 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {}
2025-01-24 12:42:30,022 ERROR frappe Error while inserting deferred Route History record: Could not find User: <EMAIL>
Site: my-site
Form Dict: {}
2025-01-25 08:30:15,728 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Gate Out Pass', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-01-25 09:04:57,920 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Gate Out Pass', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-01-25 12:02:35,782 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Gate Out Pass', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-01-25 12:04:54,388 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Gate Out Pass', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-01-25 12:29:05,480 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Gate Out Pass', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-01-25 12:29:15,796 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Gate Out Pass', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-01-25 14:02:04,166 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Container and Interchange Document Booking Datewise', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-01-25 14:03:22,388 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Container and Interchange Document Booking Datewise', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-01-31 18:23:26,157 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doctype': 'Student', 'name': 'rfigv3h2hd', 'cmd': 'frappe.desk.form.load.getdoc'}
2025-02-07 15:42:01,834 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"name":"83d9fa1ca8","owner":"Administrator","creation":"2025-02-07 15:41:34.653687","modified":"2025-02-07 15:41:34.653687","modified_by":"Administrator","docstatus":0,"idx":0,"file_name":"mine.jpg","is_private":0,"file_type":"JPG","is_home_folder":0,"is_attachments_folder":0,"file_size":69397,"file_url":"/private/files/mine.jpg","folder":"Home","is_folder":0,"attached_to_doctype":"Website Slideshow","attached_to_name":"Default Slideshow","attached_to_field":"image","content_hash":"86d4ac77ce8ff2aa2e439c0c7f95fbd2","uploaded_to_dropbox":0,"uploaded_to_google_drive":0,"doctype":"File","__last_sync_on":"2025-02-07T12:41:53.016Z","__unsaved":1}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-02-14 13:01:11,434 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'WCF Employees', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-02-14 13:01:21,711 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'WCF Employees', 'filters': '{"from_date":"2025-02-02"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-02-14 13:01:47,584 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'WCF Employees', 'filters': '{"from_date":"2022-01-01"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-02-14 13:01:53,631 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'WCF Employees', 'filters': '{"from_date":"2022-01-01","to_date":"2025-02-14"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-02-14 13:05:47,417 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'WCF Employees', 'filters': '{"from_date":"2022-01-01","to_date":"2025-02-14"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-02-14 13:06:12,025 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'WCF Employees', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-02-14 13:08:58,883 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'WCF Employees', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-02-14 13:10:26,966 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'WCF Employees', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-02-14 13:11:23,447 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'WCF Employees', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-02-14 13:15:47,266 ERROR frappe Error while inserting deferred Error Log record: Error Log 74j3m3c065: 'Title' ((1064, 'You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near \'\'\' as "national_id::200"\nfrom tabSalary Slip ss \nleft join tabSalary Detail s...\' at line 13')) will get truncated, as max characters allowed is 140
Site: explore
Form Dict: {}
2025-02-14 13:15:47,271 ERROR frappe Error while inserting deferred Error Log record: Error Log g4d8m8riir: 'Title' ((1064, 'You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near \'\'\' as "national_id::200"\nfrom tabSalary Slip ss \nleft join tabSalary Detail s...\' at line 13')) will get truncated, as max characters allowed is 140
Site: explore
Form Dict: {}
2025-02-14 13:15:47,276 ERROR frappe Error while inserting deferred Error Log record: Error Log f4rr2n51of: 'Title' ((1064, 'You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near \'\'\' as "national_id::200"\nfrom tabSalary Slip ss \nleft join tabSalary Detail s...\' at line 13')) will get truncated, as max characters allowed is 140
Site: explore
Form Dict: {}
2025-02-14 13:15:47,280 ERROR frappe Error while inserting deferred Error Log record: Error Log 6ro1n2feps: 'Title' ((1064, "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'ss\nLEFT JOIN tabSalary Detail sd ON sd.parent = ss.name\nLEFT JOIN tabEmployee...' at line 13")) will get truncated, as max characters allowed is 140
Site: explore
Form Dict: {}
2025-02-14 16:32:01,489 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'WCF Employees', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-02-18 14:39:04,374 ERROR frappe Failed to capture exception
Site: explore
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 117, in capture_exception
    set_scope(scope)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/sentry.py", line 70, in set_scope
    path = frappe.request.path
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/env/lib/python3.11/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-02-19 12:50:39,737 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-19","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"MIRZAR","party_account":"Debtors - ITL","from_posting_date":"2015-02-22","to_posting_date":"2025-02-19","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-19 12:50:56,510 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-19","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"MSEKWA CFA LIMITED","party_account":"Debtors - ITL","from_posting_date":"2015-02-22","to_posting_date":"2025-02-19","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-19 13:04:09,579 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-19","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"TOSHIBA AIRCONDITIONING","party_account":"Debtors - ITL","from_posting_date":"2015-02-22","to_posting_date":"2025-02-19","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-19 13:23:07,091 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-19","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-22","to_posting_date":"2025-02-19","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-20 12:44:33,497 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-20","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-23","to_posting_date":"2025-02-20","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-20 16:03:59,513 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-20","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-23","to_posting_date":"2025-02-20","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-20 16:04:43,004 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-20","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-23","to_posting_date":"2025-02-20","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-20 16:05:55,251 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-20","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-23","to_posting_date":"2025-02-20","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-20 16:07:30,410 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-20","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-23","to_posting_date":"2025-02-20","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-20 16:08:46,360 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-20","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-23","to_posting_date":"2025-02-20","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-20 16:12:48,286 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-20","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-23","to_posting_date":"2025-02-20","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-20 16:13:18,899 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-20","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-23","to_posting_date":"2025-02-20","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-20 16:15:00,739 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-20","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-23","to_posting_date":"2025-02-20","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-20 16:17:39,276 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-20","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-23","to_posting_date":"2025-02-20","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-20 16:45:41,306 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-20","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-23","to_posting_date":"2025-02-20","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-20 16:49:12,554 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-20","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"MIRZAR","party_account":"Debtors - ITL","from_posting_date":"2015-02-23","to_posting_date":"2025-02-20","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-20 17:00:33,475 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-20","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-23","to_posting_date":"2025-02-20","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-20 17:03:36,219 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-20","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-23","to_posting_date":"2025-02-20","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-20 17:07:51,553 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-20","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abd","party_account":"Debtors - ITL","from_posting_date":"2015-02-23","to_posting_date":"2025-02-20","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-20 17:08:39,434 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-20","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abd","party_account":"Debtors - ITL","from_posting_date":"2015-02-23","to_posting_date":"2025-02-20","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-20 17:22:03,569 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-20","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-23","to_posting_date":"2025-02-20","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-20 17:22:31,168 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-20","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abd","party_account":"Debtors - ITL","from_posting_date":"2015-02-23","to_posting_date":"2025-02-20","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-21 09:56:34,568 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'sidebar_items': '[{"type":"Wiki Sidebar","title":"Wiki","group_items":[{"type":"Wiki Page","name":"mgvrg156i5","title":"Home","route":"wiki/home"}]},{"type":"Wiki Sidebar","title":"New Group","group_items":[{"type":"Wiki Page","name":"22nv0ktfpm","title":"New Wiki Page","route":"wiki/new-wiki-page"}]},{"type":"Wiki Sidebar","title":"Wiki","group_items":[{"type":"Wiki Page","name":"mgvrg156i5","title":"Home","route":"wiki/home"}]},{"type":"Wiki Sidebar","title":"New Group","group_items":[{"type":"Wiki Page","name":"22nv0ktfpm","title":"New Wiki Page","route":"wiki/new-wiki-page"}]}]', 'cmd': 'wiki.wiki.doctype.wiki_space.wiki_space.update_sidebar'}
2025-02-21 09:56:47,980 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'sidebar_items': '[{"type":"Wiki Sidebar","title":"Wiki","group_items":[{"type":"Wiki Page","name":"mgvrg156i5","title":"Home","route":"wiki/home"}]},{"type":"Wiki Sidebar","title":"New Group","group_items":[{"type":"Wiki Page","name":"22nv0ktfpm","title":"New Wiki Page","route":"wiki/new-wiki-page"}]},{"type":"Wiki Sidebar","title":"Wiki","group_items":[{"type":"Wiki Page","name":"mgvrg156i5","title":"Home","route":"wiki/home"}]},{"type":"Wiki Sidebar","title":"New Group","group_items":[{"type":"Wiki Page","name":"22nv0ktfpm","title":"New Wiki Page","route":"wiki/new-wiki-page"}]}]', 'cmd': 'wiki.wiki.doctype.wiki_space.wiki_space.update_sidebar'}
2025-02-21 09:57:55,958 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'sidebar_items': '[{"type":"Wiki Sidebar","title":"Wiki","group_items":[{"type":"Wiki Page","name":"mgvrg156i5","title":"Home","route":"wiki/home"}]},{"type":"Wiki Sidebar","title":"New Group","group_items":[{"type":"Wiki Page","name":"22nv0ktfpm","title":"New Wiki Page","route":"wiki/new-wiki-page"}]},{"type":"Wiki Sidebar","title":"Wiki","group_items":[{"type":"Wiki Page","name":"mgvrg156i5","title":"Home","route":"wiki/home"}]},{"type":"Wiki Sidebar","title":"New Group","group_items":[{"type":"Wiki Page","name":"22nv0ktfpm","title":"New Wiki Page","route":"wiki/new-wiki-page"}]}]', 'cmd': 'wiki.wiki.doctype.wiki_space.wiki_space.update_sidebar'}
2025-02-21 09:58:01,887 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'sidebar_items': '[{"type":"Wiki Sidebar","title":"Wiki","group_items":[{"type":"Wiki Page","name":"mgvrg156i5","title":"Home","route":"wiki/home"}]},{"type":"Wiki Sidebar","title":"New Group","group_items":[{"type":"Wiki Page","name":"22nv0ktfpm","title":"New Wiki Page","route":"wiki/new-wiki-page"}]},{"type":"Wiki Sidebar","title":"Wiki","group_items":[{"type":"Wiki Page","name":"mgvrg156i5","title":"Home","route":"wiki/home"}]},{"type":"Wiki Sidebar","title":"New Group","group_items":[{"type":"Wiki Page","name":"22nv0ktfpm","title":"New Wiki Page","route":"wiki/new-wiki-page"}]}]', 'cmd': 'wiki.wiki.doctype.wiki_space.wiki_space.update_sidebar'}
2025-02-21 09:58:11,266 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'sidebar_items': '[{"type":"Wiki Sidebar","title":"Wiki","group_items":[{"type":"Wiki Page","name":"mgvrg156i5","title":"Home","route":"wiki/home"}]},{"type":"Wiki Sidebar","title":"New Group","group_items":[{"type":"Wiki Page","name":"22nv0ktfpm","title":"New Wiki Page","route":"wiki/new-wiki-page"}]},{"type":"Wiki Sidebar","title":"Wiki","group_items":[{"type":"Wiki Page","name":"mgvrg156i5","title":"Home","route":"wiki/home"}]},{"type":"Wiki Sidebar","title":"New Group","group_items":[{"type":"Wiki Page","name":"22nv0ktfpm","title":"New Wiki Page","route":"wiki/new-wiki-page"}]}]', 'cmd': 'wiki.wiki.doctype.wiki_space.wiki_space.update_sidebar'}
2025-02-21 18:12:33,214 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doctype': 'Dashboard Chart', 'name': 'Warehouse wise Stock Value', 'cmd': 'frappe.desk.form.load.getdoc'}
2025-02-21 18:12:33,217 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doctype': 'Number Card', 'name': 'Total Active Items', 'cmd': 'frappe.desk.form.load.getdoc'}
2025-02-21 18:12:33,220 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doctype': 'Number Card', 'name': 'Total Warehouses', 'cmd': 'frappe.desk.form.load.getdoc'}
2025-02-21 18:12:33,226 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doctype': 'Number Card', 'name': 'Total Stock Value', 'cmd': 'frappe.desk.form.load.getdoc'}
2025-02-24 11:57:06,511 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 11:58:39,568 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 12:00:35,653 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 12:02:14,476 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 12:11:41,224 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 12:12:51,815 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 12:19:24,268 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 12:20:29,391 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 12:21:04,938 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 12:21:58,307 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 12:24:13,045 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 12:25:33,970 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 12:26:43,979 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 12:40:15,631 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 12:41:42,476 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 12:42:28,804 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 12:43:50,347 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 12:49:45,632 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 12:52:25,504 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 12:53:52,205 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 12:56:28,230 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 12:57:53,097 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"MIRZAR","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 12:58:38,328 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 12:59:30,375 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"MIRZAR","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-24 13:06:02,343 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'args': '{"posting_date":"2025-02-24","company":"ICD Tanzania Limited","party_type":"Customer","payment_type":"Receive","party":"abc","party_account":"Debtors - ITL","from_posting_date":"2015-02-27","to_posting_date":"2025-02-24","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-02-25 09:04:48,196 ERROR frappe New Exception collected in error log
Site: rental
Form Dict: {'doctype': 'Stock Entry Detail', 'name': 'new-stock-entry-detail-cresarmpmu', 'fieldname': 'difference_account', 'value': 'Current Assets - HL', 'cmd': 'frappe.client.set_value'}
2025-02-28 13:16:53,335 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'PAYE', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-02-28 13:21:11,596 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'PAYE', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-02-28 13:25:00,641 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'PAYE', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-02-28 13:38:04,997 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'PAYE', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-02-28 13:52:48,809 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'PAYE', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-02-28 13:53:56,151 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'PAYE', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-02-28 13:55:21,786 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'PAYE', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-02-28 14:00:55,196 ERROR frappe Error while inserting deferred Error Log record: Error Log tpomuo1mal: 'Title' ((1064, "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'ss ON emp.name = ss.employee\nJOIN tabSalary Detail sd ON ss.name = sd.parent;...' at line 8")) will get truncated, as max characters allowed is 140
Site: explore
Form Dict: {}
2025-02-28 14:00:55,201 ERROR frappe Error while inserting deferred Error Log record: Error Log 7estdet6ss: 'Title' ((1064, "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'ss ON emp.name = ss.employee\nJOIN tabSalary Detail sd ON ss.name = sd.parent\n...' at line 8")) will get truncated, as max characters allowed is 140
Site: explore
Form Dict: {}
2025-02-28 14:00:55,206 ERROR frappe Error while inserting deferred Error Log record: Error Log 6ru992agr4: 'Title' ((1064, "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'ss ON emp.name = ss.employee\nJOIN tabSalary Detail sd ON ss.name = sd.parent' at line 8")) will get truncated, as max characters allowed is 140
Site: explore
Form Dict: {}
2025-02-28 14:07:40,608 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'PAYE', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-02-28 14:16:02,280 ERROR frappe Error while inserting deferred Error Log record: Error Log e7s9ts8eiv: 'Title' ((1064, "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'ss ON emp.name = ss.employee\nJOIN tabSalary Detail sd ON ss.name = sd.parent' at line 8")) will get truncated, as max characters allowed is 140
Site: explore
Form Dict: {}
2025-02-28 14:56:25,083 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'PAYE', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-03 12:17:45,884 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'args': '{"posting_date":"2025-03-03","company":"Design System","party_type":"Supplier","payment_type":"Pay","party":"vivox","party_account":"Creditors - DS","from_posting_date":"2015-03-06","to_posting_date":"2025-03-03","allocate_payment_amount":1}', 'cmd': 'csf_tz.csftz_hooks.payment_entry.get_outstanding_reference_documents'}
2025-03-06 14:31:38,505 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 14:37:04,213 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 14:40:47,154 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 14:50:15,950 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 15:16:04,840 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 15:19:03,876 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 15:33:27,424 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 15:40:55,458 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 15:45:15,635 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 16:56:33,585 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 16:59:07,197 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 17:20:46,755 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 17:21:29,367 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 17:27:55,857 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 17:31:59,267 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 17:37:08,241 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 17:42:43,896 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 18:08:42,690 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 20:28:45,978 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 20:30:46,725 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 20:50:05,336 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 20:50:38,440 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 21:06:56,691 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 21:09:47,793 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 22:31:01,248 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 22:31:01,252 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{"from_fiscal_year":"2025","to_fiscal_year":"2025","period":"Yearly","company":"Design System","budget_against":"Cost Center","budget_against_filter":[]}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-11 15:11:49,761 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'report_name': 'WCFCB Budget Variance Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-14 12:05:45,984 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doctype': 'Customer', 'name': 'Kiboko ltd', 'cmd': 'clearing.clearing.doctype.clearing_file.clearing_file.get_address_display_from_link'}
2025-03-14 13:58:11,424 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doctype': 'Customer', 'name': 'Kiboko ltd', 'cmd': 'clearing.clearing.doctype.clearing_file.clearing_file.get_address_display_from_link'}
2025-03-14 14:02:22,534 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doctype': 'Customer', 'name': 'Kiboko ltd', 'cmd': 'clearing.clearing.doctype.clearing_file.clearing_file.get_address_display_from_link'}
2025-03-14 14:20:29,983 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doctype': 'Customer', 'name': 'Kiboko ltd', 'cmd': 'clearing.clearing.doctype.clearing_file.clearing_file.get_address_display_from_link'}
2025-03-14 14:55:14,063 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"name":"CF-2025-0004","owner":"Administrator","creation":"2025-03-14 14:07:40.444758","modified":"2025-03-14 14:08:27.642493","modified_by":"Administrator","docstatus":0,"idx":0,"posting_date":"2025-03-14","company":"Design System","status":"On Process","customer":"Kiboko ltd","shipper":"John Doe","shipment_type":"Import","mode_of_transport":"Truck","bl_type":"Original B/L","departure_date":"2025-03-02","arrival_date":"2025-03-20","cargo_country_of_origin":"Tanzania","cargo_location":"Tanzania","tancis_lodging_date":"2025-03-04","reference_no":"23RE45","tansad_no":"T234D45","declaration_type":"sent","cl_plan":"fgt","doctype":"Clearing File","document":[{"name":"grkh0iovbk","owner":"Administrator","creation":"2025-03-14 14:07:40.444758","modified":"2025-03-14 14:08:27.642493","modified_by":"Administrator","docstatus":0,"idx":1,"clearing_document_id":"Bill of Lading B/L-CF-2025-0004-0005","document_name":"Bill of Lading B/L","document_received":1,"submission_date":"2025-03-14 14:07:47.397981","document_attributes":"","parent":"CF-2025-0004","parentfield":"document","parenttype":"Clearing File","doctype":"Clearing File Document"}],"cargo_details":[{"name":"dib1rkvftq","owner":"Administrator","creation":"2025-03-14 14:07:40.444758","modified":"2025-03-14 14:08:27.642493","modified_by":"Administrator","docstatus":0,"idx":1,"package_type":"Container 20FT","cargo_description":"my cargo type","number_of_packages":0,"weight":0,"volume":0,"country_of_origin":"Tanzania","value":68900,"currency":"TZS","trading_country":"Tanzania","country_last_consignment":"Tanzania","parent":"CF-2025-0004","parentfield":"cargo_details","parenttype":"Clearing File","doctype":"Cargo"}],"__last_sync_on":"2025-03-14T11:55:10.214Z"}', 'action': 'Submit', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-03-14 14:58:17,878 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"name":"CF-2025-0004","owner":"Administrator","creation":"2025-03-14 14:07:40.444758","modified":"2025-03-14 14:08:27.642493","modified_by":"Administrator","docstatus":0,"idx":0,"posting_date":"2025-03-14","company":"Design System","status":"On Process","customer":"Kiboko ltd","shipper":"John Doe","shipment_type":"Import","mode_of_transport":"Truck","bl_type":"Original B/L","departure_date":"2025-03-02","arrival_date":"2025-03-20","cargo_country_of_origin":"Tanzania","cargo_location":"Tanzania","tancis_lodging_date":"2025-03-04","reference_no":"23RE45","tansad_no":"T234D45","declaration_type":"sent","cl_plan":"fgt","doctype":"Clearing File","document":[{"name":"grkh0iovbk","owner":"Administrator","creation":"2025-03-14 14:07:40.444758","modified":"2025-03-14 14:08:27.642493","modified_by":"Administrator","docstatus":0,"idx":1,"clearing_document_id":"Bill of Lading B/L-CF-2025-0004-0005","document_name":"Bill of Lading B/L","document_received":1,"submission_date":"2025-03-14 14:07:47.397981","document_attributes":"","parent":"CF-2025-0004","parentfield":"document","parenttype":"Clearing File","doctype":"Clearing File Document"}],"cargo_details":[{"name":"dib1rkvftq","owner":"Administrator","creation":"2025-03-14 14:07:40.444758","modified":"2025-03-14 14:08:27.642493","modified_by":"Administrator","docstatus":0,"idx":1,"package_type":"Container 20FT","cargo_description":"my cargo type","number_of_packages":0,"weight":0,"volume":0,"country_of_origin":"Tanzania","value":68900,"currency":"TZS","trading_country":"Tanzania","country_last_consignment":"Tanzania","parent":"CF-2025-0004","parentfield":"cargo_details","parenttype":"Clearing File","doctype":"Cargo"}],"__last_sync_on":"2025-03-14T11:58:12.325Z"}', 'action': 'Submit', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-03-14 15:12:49,888 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doc': '{"name":"CF-2025-0004","owner":"Administrator","creation":"2025-03-14 14:07:40.444758","modified":"2025-03-14 14:08:27.642493","modified_by":"Administrator","docstatus":0,"idx":0,"posting_date":"2025-03-14","company":"Design System","status":"On Process","customer":"Kiboko ltd","shipper":"John Doe","shipment_type":"Import","mode_of_transport":"Truck","bl_type":"Original B/L","departure_date":"2025-03-02","arrival_date":"2025-03-20","cargo_country_of_origin":"Tanzania","cargo_location":"Tanzania","tancis_lodging_date":"2025-03-04","reference_no":"23RE45","tansad_no":"T234D45","declaration_type":"sent","cl_plan":"fgt","doctype":"Clearing File","document":[{"name":"grkh0iovbk","owner":"Administrator","creation":"2025-03-14 14:07:40.444758","modified":"2025-03-14 14:08:27.642493","modified_by":"Administrator","docstatus":0,"idx":1,"clearing_document_id":"Bill of Lading B/L-CF-2025-0004-0005","document_name":"Bill of Lading B/L","document_received":1,"submission_date":"2025-03-14 14:07:47.397981","document_attributes":"","parent":"CF-2025-0004","parentfield":"document","parenttype":"Clearing File","doctype":"Clearing File Document"}],"cargo_details":[{"name":"dib1rkvftq","owner":"Administrator","creation":"2025-03-14 14:07:40.444758","modified":"2025-03-14 14:08:27.642493","modified_by":"Administrator","docstatus":0,"idx":1,"package_type":"Container 20FT","cargo_description":"my cargo type","number_of_packages":0,"weight":0,"volume":0,"country_of_origin":"Tanzania","value":68900,"currency":"TZS","trading_country":"Tanzania","country_last_consignment":"Tanzania","parent":"CF-2025-0004","parentfield":"cargo_details","parenttype":"Clearing File","doctype":"Cargo"}],"__last_sync_on":"2025-03-14T12:12:45.091Z"}', 'action': 'Submit', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-03-17 17:42:06,215 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doctype': 'Customer', 'name': 'Kiboko ltd', 'cmd': 'clearing.clearing.doctype.clearing_file.clearing_file.get_address_display_from_link'}
2025-03-17 17:43:01,616 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doctype': 'Customer', 'name': 'Kiboko ltd', 'cmd': 'clearing.clearing.doctype.clearing_file.clearing_file.get_address_display_from_link'}
2025-03-17 17:46:27,984 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doctype': 'Customer', 'name': 'Kiboko ltd', 'cmd': 'clearing.clearing.doctype.clearing_file.clearing_file.get_address_display_from_link'}
2025-03-17 18:03:06,981 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doctype': 'Customer', 'name': 'Kiboko ltd', 'cmd': 'clearing.clearing.doctype.clearing_file.clearing_file.get_address_display_from_link'}
2025-03-17 18:03:35,608 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doctype': 'Customer', 'name': 'Kiboko ltd', 'cmd': 'clearing.clearing.doctype.clearing_file.clearing_file.get_address_display_from_link'}
2025-03-17 18:06:13,767 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doctype': 'Customer', 'name': 'Kiboko ltd', 'cmd': 'clearing.clearing.doctype.clearing_file.clearing_file.get_address_display_from_link'}
2025-03-17 18:19:29,459 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doctype': 'Customer', 'name': 'kibo', 'cmd': 'clearing.clearing.doctype.clearing_file.clearing_file.get_address_display_from_link'}
2025-03-17 18:20:37,231 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doctype': 'Customer', 'name': 'kibo', 'cmd': 'clearing.clearing.doctype.clearing_file.clearing_file.get_address_display_from_link'}
2025-03-17 21:16:18,317 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doctype': 'Customer', 'name': 'kibo', 'cmd': 'clearing.clearing.doctype.clearing_file.clearing_file.get_address_display_from_link'}
2025-03-17 21:18:29,463 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'doctype': 'Customer', 'name': 'kibo', 'cmd': 'clearing.clearing.doctype.clearing_file.clearing_file.get_address_display_from_link'}
2025-03-19 17:30:22,427 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Current Container Stock Report', 'filters': '{"status_filter":"In House"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-19 17:30:31,254 ERROR frappe Error while inserting deferred Error Log record: Error Log f55p3gt8cu: 'Title' ((1064, "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '}' at line 22")) will get truncated, as max characters allowed is 140
Site: explore
Form Dict: {}
2025-03-19 17:32:50,316 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Current Container Stock Report', 'filters': '{"status_filter":"In House"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-19 17:35:44,481 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Current Container Stock Report', 'filters': '{"status_filter":"In House"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-19 17:35:50,807 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Current Container Stock Report', 'filters': '{"status_filter":"Delivered"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-03-19 17:35:56,933 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Current Container Stock Report', 'filters': '{"status_filter":"In House"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-03-19 17:44:38,996 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Current Container Stock Report', 'filters': '{"status_filter":"In House"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-19 17:45:36,540 ERROR frappe Error while inserting deferred Error Log record: Error Log 0laeaq2sp3: 'Title' ((1064, "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '}' at line 22")) will get truncated, as max characters allowed is 140
Site: explore
Form Dict: {}
2025-03-19 17:45:36,543 ERROR frappe Error while inserting deferred Error Log record: Error Log bk5n82hvuq: 'Title' ((1064, "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '}' at line 22")) will get truncated, as max characters allowed is 140
Site: explore
Form Dict: {}
2025-03-19 17:45:36,547 ERROR frappe Error while inserting deferred Error Log record: Error Log 1k60j73lha: 'Title' ((1064, "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '}' at line 22")) will get truncated, as max characters allowed is 140
Site: explore
Form Dict: {}
2025-03-19 17:45:36,550 ERROR frappe Error while inserting deferred Error Log record: Error Log 3cpng6i5n4: 'Title' ((1064, "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '}' at line 22")) will get truncated, as max characters allowed is 140
Site: explore
Form Dict: {}
2025-03-19 17:51:25,978 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Current Container Stock Report', 'filters': '{"status_filter":"In House"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-19 17:53:05,470 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Current Container Stock Report', 'filters': '{"status_filter":"In House"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-03-19 17:58:23,174 ERROR frappe New Exception collected in error log
Site: explore
Form Dict: {'report_name': 'Current Container Stock Report', 'filters': '{"status_filter":"Delivered"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-03-19 18:32:28,332 ERROR frappe Could not take error snapshot: No module named 'flexible_budget'
Site: working
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 99, in application
    init_request(request)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 172, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1650, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1498, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1463, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1493, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'flexible_budget'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
                ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1291, in get_doc
    doc = frappe.model.document.get_doc(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1599, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 47, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1568, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'flexible_budget'
2025-03-19 18:32:33,766 ERROR frappe Could not take error snapshot: No module named 'flexible_budget'
Site: working
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 99, in application
    init_request(request)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 172, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1650, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1498, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1463, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1493, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'flexible_budget'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
                ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1291, in get_doc
    doc = frappe.model.document.get_doc(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1599, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 47, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1568, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'flexible_budget'
2025-03-20 00:58:26,571 ERROR frappe New Exception collected in error log
Site: working
Form Dict: {'docs': '{"name":"Weekly Target","creation":"2025-03-18 15:00:14.456073","modified":"2025-03-20 00:38:18.900163","modified_by":"Administrator","owner":"Administrator","docstatus":0,"idx":0,"issingle":0,"is_virtual":0,"is_tree":0,"istable":0,"editable_grid":0,"track_changes":1,"module":"Flexible Budget","autoname":"field:title","naming_rule":"By fieldname","title_field":"company","sort_field":"modified","sort_order":"DESC","read_only":0,"in_create":0,"allow_copy":0,"allow_rename":1,"allow_import":0,"hide_toolbar":0,"track_seen":0,"max_attachments":0,"engine":"InnoDB","is_submittable":0,"show_name_in_global_search":0,"custom":0,"beta":0,"has_web_view":0,"allow_guest_to_view":0,"email_append_to":0,"show_title_field_in_link":0,"migration_hash":"ede95b346820452efdefec8f4248d529","translated_doctype":0,"is_calendar_and_gantt":0,"quick_entry":0,"track_views":0,"queue_in_background":0,"allow_events_in_timeline":0,"allow_auto_repeat":0,"make_attachments_public":0,"force_re_route_to_default_view":0,"show_preview_popup":0,"index_web_pages_for_search":1,"doctype":"DocType","permissions":[{"name":"scl1mcje80","creation":"2025-03-18 15:00:14.456073","modified":"2025-03-20 00:38:18.900163","modified_by":"Administrator","owner":"Administrator","docstatus":0,"parent":"Weekly Target","parentfield":"permissions","parenttype":"DocType","idx":1,"permlevel":0,"role":"System Manager","read":1,"write":1,"create":1,"submit":0,"cancel":0,"delete":1,"amend":0,"report":1,"export":1,"import":0,"share":1,"print":1,"email":1,"if_owner":0,"select":0,"doctype":"DocPerm"}],"fields":[{"name":"1f1p9v5crd","creation":"2025-03-18 15:00:14.456073","modified":"2025-03-20 00:38:18.900163","modified_by":"Administrator","owner":"Administrator","docstatus":0,"parent":"Weekly Target","parentfield":"fields","parenttype":"DocType","idx":1,"fieldname":"title","label":"Name","fieldtype":"Data","search_index":0,"show_dashboard":0,"hidden":0,"set_only_once":0,"allow_in_quick_entry":0,"print_hide":0,"report_hide":0,"reqd":0,"bold":0,"in_global_search":0,"collapsible":0,"unique":1,"no_copy":0,"allow_on_submit":0,"show_preview_popup":0,"permlevel":0,"ignore_user_permissions":0,"columns":0,"in_list_view":0,"fetch_if_empty":0,"in_filter":0,"remember_last_selected_value":0,"ignore_xss_filter":0,"print_hide_if_no_value":0,"allow_bulk_edit":0,"in_standard_filter":0,"in_preview":0,"read_only":0,"precision":"","length":0,"translatable":0,"hide_border":0,"hide_days":0,"hide_seconds":0,"non_negative":0,"is_virtual":0,"sort_options":0,"show_on_timeline":0,"make_attachment_public":0,"doctype":"DocField"},{"name":"5pcuqnae86","creation":"2025-03-18 15:00:14.456073","modified":"2025-03-20 00:38:18.900163","modified_by":"Administrator","owner":"Administrator","docstatus":0,"parent":"Weekly Target","parentfield":"fields","parenttype":"DocType","idx":2,"fieldname":"company","label":"Company","fieldtype":"Link","options":"Company","search_index":0,"show_dashboard":0,"hidden":0,"set_only_once":0,"allow_in_quick_entry":0,"print_hide":0,"report_hide":0,"reqd":0,"bold":0,"in_global_search":0,"collapsible":0,"unique":0,"no_copy":0,"allow_on_submit":0,"show_preview_popup":0,"permlevel":0,"ignore_user_permissions":0,"columns":0,"in_list_view":0,"fetch_if_empty":0,"in_filter":0,"remember_last_selected_value":0,"ignore_xss_filter":0,"print_hide_if_no_value":0,"allow_bulk_edit":0,"in_standard_filter":0,"in_preview":0,"read_only":0,"precision":"","length":0,"translatable":0,"hide_border":0,"hide_days":0,"hide_seconds":0,"non_negative":0,"is_virtual":0,"sort_options":0,"show_on_timeline":0,"make_attachment_public":0,"doctype":"DocField"},{"name":"4mruet902f","creation":"2025-03-18 15:00:14.456073","modified":"2025-03-20 00:38:18.900163","modified_by":"Administrator","owner":"Administrator","docstatus":0,"parent":"Weekly Target","parentfield":"fields","parenttype":"DocType","idx":3,"fieldname":"week_no","label":"Week No","fieldtype":"Int","search_index":0,"show_dashboard":0,"hidden":0,"set_only_once":0,"allow_in_quick_entry":0,"print_hide":0,"report_hide":0,"reqd":0,"bold":0,"in_global_search":0,"collapsible":0,"unique":0,"no_copy":0,"allow_on_submit":0,"show_preview_popup":0,"permlevel":0,"ignore_user_permissions":0,"columns":0,"in_list_view":1,"fetch_if_empty":0,"in_filter":0,"remember_last_selected_value":0,"ignore_xss_filter":0,"print_hide_if_no_value":0,"allow_bulk_edit":0,"in_standard_filter":0,"in_preview":0,"read_only":0,"precision":"","length":0,"translatable":0,"hide_border":0,"hide_days":0,"hide_seconds":0,"non_negative":0,"is_virtual":0,"sort_options":0,"show_on_timeline":0,"make_attachment_public":0,"doctype":"DocField"},{"name":"1t1gqnqsvv","creation":"2025-03-18 15:00:14.456073","modified":"2025-03-20 00:38:18.900163","modified_by":"Administrator","owner":"Administrator","docstatus":0,"parent":"Weekly Target","parentfield":"fields","parenttype":"DocType","idx":4,"fieldname":"start_date","label":"Week Start Date","fieldtype":"Date","search_index":0,"show_dashboard":0,"hidden":0,"set_only_once":0,"allow_in_quick_entry":0,"print_hide":0,"report_hide":0,"reqd":1,"bold":0,"in_global_search":0,"collapsible":0,"unique":0,"no_copy":0,"allow_on_submit":0,"show_preview_popup":0,"permlevel":0,"ignore_user_permissions":0,"columns":0,"in_list_view":0,"fetch_if_empty":0,"in_filter":0,"remember_last_selected_value":0,"ignore_xss_filter":0,"print_hide_if_no_value":0,"allow_bulk_edit":0,"in_standard_filter":0,"in_preview":0,"read_only":0,"precision":"","length":0,"translatable":0,"hide_border":0,"hide_days":0,"hide_seconds":0,"non_negative":0,"is_virtual":0,"sort_options":0,"show_on_timeline":0,"make_attachment_public":0,"doctype":"DocField"},{"name":"4j2n5d5r9l","creation":"2025-03-18 15:00:14.456073","modified":"2025-03-20 00:38:18.900163","modified_by":"Administrator","owner":"Administrator","docstatus":0,"parent":"Weekly Target","parentfield":"fields","parenttype":"DocType","idx":5,"fieldname":"end_date","label":"Week End Date","fieldtype":"Date","search_index":0,"show_dashboard":0,"hidden":0,"set_only_once":0,"allow_in_quick_entry":0,"print_hide":0,"report_hide":0,"reqd":1,"bold":0,"in_global_search":0,"collapsible":0,"unique":0,"no_copy":0,"allow_on_submit":0,"show_preview_popup":0,"permlevel":0,"ignore_user_permissions":0,"columns":0,"in_list_view":0,"fetch_if_empty":0,"in_filter":0,"remember_last_selected_value":0,"ignore_xss_filter":0,"print_hide_if_no_value":0,"allow_bulk_edit":0,"in_standard_filter":0,"in_preview":0,"read_only":0,"precision":"","length":0,"translatable":0,"hide_border":0,"hide_days":0,"hide_seconds":0,"non_negative":0,"is_virtual":0,"sort_options":0,"show_on_timeline":0,"make_attachment_public":0,"doctype":"DocField"},{"name":"h6kss84lnd","creation":"2025-03-18 15:00:14.456073","modified":"2025-03-20 00:38:18.900163","modified_by":"Administrator","owner":"Administrator","docstatus":0,"parent":"Weekly Target","parentfield":"fields","parenttype":"DocType","idx":6,"fieldname":"target_amount","label":"Target Amount","fieldtype":"Currency","search_index":0,"show_dashboard":0,"hidden":0,"set_only_once":0,"allow_in_quick_entry":0,"print_hide":0,"report_hide":0,"reqd":1,"bold":0,"in_global_search":0,"collapsible":0,"unique":0,"no_copy":0,"allow_on_submit":0,"show_preview_popup":0,"permlevel":0,"ignore_user_permissions":0,"columns":0,"in_list_view":0,"fetch_if_empty":0,"in_filter":0,"remember_last_selected_value":0,"ignore_xss_filter":0,"print_hide_if_no_value":0,"allow_bulk_edit":0,"in_standard_filter":0,"in_preview":0,"read_only":0,"precision":"","length":0,"translatable":0,"hide_border":0,"hide_days":0,"hide_seconds":0,"non_negative":0,"is_virtual":0,"sort_options":0,"show_on_timeline":0,"make_attachment_public":0,"doctype":"DocField"},{"name":"hb54fcc51c","creation":"2025-03-18 15:00:14.456073","modified":"2025-03-20 00:38:18.900163","modified_by":"Administrator","owner":"Administrator","docstatus":0,"parent":"Weekly Target","parentfield":"fields","parenttype":"DocType","idx":7,"fieldname":"actual_amount","label":"Actual Amount","fieldtype":"Currency","search_index":0,"show_dashboard":0,"hidden":0,"set_only_once":0,"allow_in_quick_entry":0,"print_hide":0,"report_hide":0,"reqd":0,"bold":0,"in_global_search":0,"collapsible":0,"unique":0,"no_copy":0,"allow_on_submit":0,"show_preview_popup":0,"permlevel":0,"ignore_user_permissions":0,"columns":0,"in_list_view":1,"fetch_if_empty":0,"in_filter":0,"remember_last_selected_value":0,"ignore_xss_filter":0,"print_hide_if_no_value":0,"allow_bulk_edit":0,"in_standard_filter":0,"in_preview":0,"read_only":1,"precision":"","length":0,"translatable":0,"hide_border":0,"hide_days":0,"hide_seconds":0,"non_negative":0,"is_virtual":0,"sort_options":0,"show_on_timeline":0,"make_attachment_public":0,"doctype":"DocField"},{"name":"krrjor8p85","creation":"2025-03-18 15:00:14.456073","modified":"2025-03-20 00:38:18.900163","modified_by":"Administrator","owner":"Administrator","docstatus":0,"parent":"Weekly Target","parentfield":"fields","parenttype":"DocType","idx":8,"fieldname":"progress","label":"Progress","fieldtype":"Percent","search_index":0,"show_dashboard":0,"hidden":0,"set_only_once":0,"allow_in_quick_entry":0,"print_hide":0,"report_hide":0,"reqd":0,"bold":0,"in_global_search":0,"collapsible":0,"unique":0,"no_copy":0,"allow_on_submit":0,"show_preview_popup":0,"depends_on":"eval:doc.target_amount > 0","permlevel":0,"ignore_user_permissions":0,"columns":0,"in_list_view":1,"fetch_if_empty":0,"in_filter":0,"remember_last_selected_value":0,"ignore_xss_filter":0,"print_hide_if_no_value":0,"allow_bulk_edit":0,"in_standard_filter":0,"in_preview":0,"read_only":1,"precision":"","length":0,"translatable":0,"hide_border":0,"hide_days":0,"hide_seconds":0,"non_negative":0,"is_virtual":0,"sort_options":0,"show_on_timeline":0,"make_attachment_public":0,"doctype":"DocField"},{"name":"18uttmtalo","creation":"2025-03-18 15:00:14.456073","modified":"2025-03-20 00:38:18.900163","modified_by":"Administrator","owner":"Administrator","docstatus":0,"parent":"Weekly Target","parentfield":"fields","parenttype":"DocType","idx":9,"fieldname":"column_break_hoar","fieldtype":"Column Break","search_index":0,"show_dashboard":0,"hidden":0,"set_only_once":0,"allow_in_quick_entry":0,"print_hide":0,"report_hide":0,"reqd":0,"bold":0,"in_global_search":0,"collapsible":0,"unique":0,"no_copy":0,"allow_on_submit":0,"show_preview_popup":0,"permlevel":0,"ignore_user_permissions":0,"columns":0,"in_list_view":0,"fetch_if_empty":0,"in_filter":0,"remember_last_selected_value":0,"ignore_xss_filter":0,"print_hide_if_no_value":0,"allow_bulk_edit":0,"in_standard_filter":0,"in_preview":0,"read_only":0,"precision":"","length":0,"translatable":0,"hide_border":0,"hide_days":0,"hide_seconds":0,"non_negative":0,"is_virtual":0,"sort_options":0,"show_on_timeline":0,"make_attachment_public":0,"doctype":"DocField"},{"name":"g8165ql5pu","creation":"2025-03-18 15:00:14.456073","modified":"2025-03-20 00:38:18.900163","modified_by":"Administrator","owner":"Administrator","docstatus":0,"parent":"Weekly Target","parentfield":"fields","parenttype":"DocType","idx":10,"fieldname":"branch","label":"Branch","fieldtype":"Link","options":"Branch","search_index":0,"show_dashboard":0,"hidden":0,"set_only_once":0,"allow_in_quick_entry":0,"print_hide":0,"report_hide":0,"reqd":0,"bold":0,"in_global_search":0,"collapsible":0,"unique":0,"no_copy":0,"allow_on_submit":0,"show_preview_popup":0,"permlevel":0,"ignore_user_permissions":0,"columns":0,"in_list_view":0,"fetch_if_empty":0,"in_filter":0,"remember_last_selected_value":0,"ignore_xss_filter":0,"print_hide_if_no_value":0,"allow_bulk_edit":0,"in_standard_filter":0,"in_preview":0,"read_only":0,"precision":"","length":0,"translatable":0,"hide_border":0,"hide_days":0,"hide_seconds":0,"non_negative":0,"is_virtual":0,"sort_options":0,"show_on_timeline":0,"make_attachment_public":0,"doctype":"DocField"},{"name":"celakjbplk","creation":"2025-03-18 15:00:14.456073","modified":"2025-03-20 00:38:18.900163","modified_by":"Administrator","owner":"Administrator","docstatus":0,"parent":"Weekly Target","parentfield":"fields","parenttype":"DocType","idx":11,"fieldname":"department","label":"Department","fieldtype":"Link","options":"Department","search_index":0,"show_dashboard":0,"hidden":0,"set_only_once":0,"allow_in_quick_entry":0,"print_hide":0,"report_hide":0,"reqd":0,"bold":0,"in_global_search":0,"collapsible":0,"unique":0,"no_copy":0,"allow_on_submit":0,"show_preview_popup":0,"permlevel":0,"ignore_user_permissions":0,"columns":0,"in_list_view":0,"fetch_if_empty":0,"in_filter":0,"remember_last_selected_value":0,"ignore_xss_filter":0,"print_hide_if_no_value":0,"allow_bulk_edit":0,"in_standard_filter":0,"in_preview":0,"read_only":0,"precision":"","length":0,"translatable":0,"hide_border":0,"hide_days":0,"hide_seconds":0,"non_negative":0,"is_virtual":0,"sort_options":0,"show_on_timeline":0,"make_attachment_public":0,"doctype":"DocField"},{"name":"oqa3tvo5kf","creation":"2025-03-18 15:00:14.456073","modified":"2025-03-20 00:38:18.900163","modified_by":"Administrator","owner":"Administrator","docstatus":0,"parent":"Weekly Target","parentfield":"fields","parenttype":"DocType","idx":12,"fieldname":"cost_center","label":"Cost Center","fieldtype":"Link","options":"Cost Center","search_index":0,"show_dashboard":0,"hidden":0,"set_only_once":0,"allow_in_quick_entry":0,"print_hide":0,"report_hide":0,"reqd":0,"bold":0,"in_global_search":0,"collapsible":0,"unique":0,"no_copy":0,"allow_on_submit":0,"show_preview_popup":0,"permlevel":0,"ignore_user_permissions":0,"columns":0,"in_list_view":0,"fetch_if_empty":0,"in_filter":0,"remember_last_selected_value":0,"ignore_xss_filter":0,"print_hide_if_no_value":0,"allow_bulk_edit":0,"in_standard_filter":0,"in_preview":0,"read_only":0,"precision":"","length":0,"translatable":0,"hide_border":0,"hide_days":0,"hide_seconds":0,"non_negative":0,"is_virtual":0,"sort_options":0,"show_on_timeline":0,"make_attachment_public":0,"doctype":"DocField"}],"actions":[],"links":[],"states":[],"__last_sync_on":"2025-03-19T21:58:26.302Z"}', 'method': 'check_pending_migration', 'cmd': 'run_doc_method'}
