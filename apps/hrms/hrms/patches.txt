[pre_model_sync]
hrms.patches.v15_0.check_version_compatibility_with_frappe #2023-06-27

[post_model_sync]
hrms.patches.post_install.set_payroll_entry_status
hrms.patches.v1_0.rearrange_employee_fields
hrms.patches.post_install.update_allocate_on_in_leave_type
hrms.patches.v14_0.create_custom_field_for_appraisal_template
hrms.patches.post_install.update_performance_module_changes #2023-04-17
hrms.patches.v14_0.update_payroll_frequency_to_none_if_salary_slip_is_based_on_timesheet
hrms.patches.v14_0.update_ess_user_access #2023-08-14
execute:frappe.db.set_default("date_format", frappe.db.get_single_value("System Settings", "date_format"))
hrms.patches.v14_0.create_vehicle_service_item
hrms.patches.v14_0.add_expense_claim_to_repost_settings
hrms.patches.v15_0.notify_about_loan_app_separation
hrms.patches.v15_0.rename_enable_late_entry_early_exit_grace_period
hrms.patches.v14_0.update_repay_from_salary_and_payroll_payable_account_fields
hrms.patches.v14_0.create_custom_field_in_loan
hrms.patches.v14_0.update_loan_repayment_repay_from_salary
hrms.patches.v15_0.migrate_loan_type_to_loan_product
hrms.patches.v15_0.rename_and_update_leave_encashment_fields
hrms.patches.v14_0.update_title_in_employee_onboarding_and_separation_templates
hrms.patches.v15_0.make_hr_settings_tab_in_company_master
hrms.patches.v15_0.enable_allow_checkin_setting
hrms.patches.v15_0.set_default_asset_action_in_fnf
hrms.patches.v15_0.add_loan_docperms_to_ess #2024-05-14
hrms.patches.v15_0.migrate_shift_assignment_schedule_to_shift_schedule
hrms.patches.v15_0.update_payment_status_for_leave_encashment
hrms.patches.v15_0.create_accounting_dimensions_in_leave_encashment
hrms.patches.v15_0.set_half_day_status_to_present_in_exisiting_half_day_attendance
hrms.patches.v14_0.create_marginal_relief_field_for_india_localisation
hrms.patches.v15_0.create_marginal_relief_field_for_india_localisation
hrms.patches.v15_0.fix_timesheet_status