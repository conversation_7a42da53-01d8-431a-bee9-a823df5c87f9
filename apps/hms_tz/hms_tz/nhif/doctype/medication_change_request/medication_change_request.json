{"actions": [], "allow_copy": 1, "allow_rename": 1, "autoname": "HLC-MCR-.#####", "creation": "2021-02-13 22:35:28.791881", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["title", "patient", "patient_name", "appointment", "column_break_5", "healthcare_practitioner", "practitioner_name", "medical_department", "column_break_2nqfn", "company", "patient_encounter", "delivery_note", "sales_order", "column_break_usdp0", "posting_date", "hms_tz_comment", "warehouse", "section_break_2fstq", "insurance_subscription", "column_break_cbx9t", "insurance_coverage_plan", "column_break_k4mgj", "insurance_company", "section_break_12", "patient_encounter_final_diagnosis", "medication_orders_section", "original_pharmacy_prescription", "drug_prescription", "amended_from"], "fields": [{"fieldname": "title", "fieldtype": "Data", "hidden": 1, "label": "Title", "read_only": 1, "search_index": 1}, {"fetch_from": "patient_encounter.appointment", "fieldname": "appointment", "fieldtype": "Link", "label": "Appointment", "options": "Patient Appointment", "read_only": 1, "reqd": 1, "search_index": 1}, {"fieldname": "patient", "fieldtype": "Link", "in_list_view": 1, "label": "Patient", "options": "Patient", "reqd": 1, "search_index": 1}, {"fetch_from": "patient.patient_name", "fieldname": "patient_name", "fieldtype": "Data", "label": "Patient Name", "read_only": 1}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fetch_from": "patient_encounter.company", "fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "read_only": 1, "search_index": 1}, {"fieldname": "patient_encounter", "fieldtype": "Link", "in_list_view": 1, "label": "Patient Encounter", "options": "Patient Encounter", "reqd": 1, "search_index": 1}, {"depends_on": "eval: !doc.sales_order", "fieldname": "delivery_note", "fieldtype": "Link", "in_list_view": 1, "label": "Delivery Note", "mandatory_depends_on": "eval: !doc.sales_order", "options": "Delivery Note", "search_index": 1}, {"fetch_from": "patient_encounter.practitioner", "fieldname": "healthcare_practitioner", "fieldtype": "Link", "label": "Healthcare Practitioner", "options": "Healthcare Practitioner", "read_only": 1, "search_index": 1}, {"fetch_from": "healthcare_practitioner.first_name", "fieldname": "practitioner_name", "fieldtype": "Data", "label": "Practitioner Name", "read_only": 1}, {"fetch_from": "patient_encounter.medical_department", "fieldname": "medical_department", "fieldtype": "Link", "label": "Medical Department", "options": "Medical Department", "read_only": 1}, {"fieldname": "section_break_12", "fieldtype": "Section Break"}, {"fetch_if_empty": 1, "fieldname": "patient_encounter_final_diagnosis", "fieldtype": "Table", "label": "Final Diagnosis", "options": "Codification Table", "read_only": 1}, {"fieldname": "medication_orders_section", "fieldtype": "Section Break", "label": "Medication Orders"}, {"fieldname": "original_pharmacy_prescription", "fieldtype": "Table", "label": "Original Pharmacy Prescription", "options": "Drug Prescription", "read_only": 1}, {"fieldname": "drug_prescription", "fieldtype": "Table", "label": "Pharmacy Prescription", "options": "Drug Prescription"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Medication Change Request", "print_hide": 1, "read_only": 1}, {"description": "comment indicates an item that required to be changed", "fieldname": "hms_tz_comment", "fieldtype": "Small Text", "label": "Comment", "read_only": 1}, {"depends_on": "eval: !doc.delivery_note", "fieldname": "sales_order", "fieldtype": "Link", "label": "Sales Order", "mandatory_depends_on": "eval: !doc.delivery_note", "options": "Sales Order", "search_index": 1}, {"fieldname": "section_break_2fstq", "fieldtype": "Section Break"}, {"fieldname": "insurance_subscription", "fieldtype": "Link", "label": "Insurance Subscription", "options": "Healthcare Insurance Subscription", "read_only": 1, "search_index": 1}, {"fieldname": "column_break_cbx9t", "fieldtype": "Column Break"}, {"fieldname": "insurance_coverage_plan", "fieldtype": "Data", "label": "Insurance Coverage Plan", "read_only": 1, "search_index": 1}, {"fieldname": "column_break_k4mgj", "fieldtype": "Column Break"}, {"fieldname": "insurance_company", "fieldtype": "Link", "label": "Insurance Company", "options": "Healthcare Insurance Company", "read_only": 1, "search_index": 1}, {"fieldname": "column_break_2nqfn", "fieldtype": "Column Break"}, {"fieldname": "column_break_usdp0", "fieldtype": "Column Break"}, {"fieldname": "posting_date", "fieldtype": "Date", "label": "Posting Date", "read_only": 1, "search_index": 1}, {"fieldname": "warehouse", "fieldtype": "Data", "hidden": 1, "label": "Warehouse", "read_only": 1, "search_index": 1}], "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2025-07-06 18:38:11.174138", "modified_by": "Administrator", "module": "NHIF", "name": "Medication Change Request", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}], "restrict_to_domain": "Healthcare", "search_fields": "patient, healthcare_practitioner, medical_department", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "title", "track_changes": 1}