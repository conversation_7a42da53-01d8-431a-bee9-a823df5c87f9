{"actions": [], "allow_rename": 1, "autoname": "hash", "creation": "2025-05-12 10:30:13.295562", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["patient", "patient_name", "customer", "column_break_cejb2", "appointment", "source_doctype", "source_docname", "column_break_0l72r", "posting_date", "company", "section_break_hx3mw", "payment_type", "mode_of_payment", "insurance_subscription", "column_break_ckt1u", "insurance_company", "insurance_coverage_plan", "section_break_my8su", "service_type", "service_name", "item_code", "price_list", "column_break_9wgfq", "currency", "rate", "percent_covered", "column_break_iwfua", "qty", "qty_returned", "amount", "section_break_efosd", "lrpmt_doctype", "lrpmt_docname", "dn_detail", "column_break_pufom", "ref_doctype", "ref_docname", "column_break_eza6d", "is_cancelled", "lrpmt_status", "sales_invoice", "section_break_4zy49", "healthcare_practitioner", "healthcare_service_unit", "column_break_nkfo7", "department", "cost_center", "section_break_frwzc", "created_by", "updated_by", "column_break_gzouw", "updated_from_doctype", "updated_from_docname", "column_break_b5uew", "remarks"], "fields": [{"fieldname": "posting_date", "fieldtype": "Data", "in_standard_filter": 1, "label": "Posting Date", "read_only": 1, "search_index": 1}, {"fieldname": "company", "fieldtype": "Data", "in_standard_filter": 1, "label": "Company", "read_only": 1, "search_index": 1}, {"fieldname": "service_type", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Service Type", "read_only": 1, "search_index": 1}, {"fieldname": "service_name", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Service Name", "read_only": 1, "search_index": 1}, {"fieldname": "item_code", "fieldtype": "Data", "label": "Insurance Item Code", "read_only": 1, "search_index": 1}, {"fieldname": "patient", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Patient", "read_only": 1, "search_index": 1}, {"fieldname": "patient_name", "fieldtype": "Data", "in_standard_filter": 1, "label": "Patient Name", "read_only": 1, "search_index": 1}, {"fieldname": "appointment", "fieldtype": "Data", "in_standard_filter": 1, "label": "Patient Appointment", "read_only": 1, "search_index": 1}, {"fieldname": "source_doctype", "fieldtype": "Data", "label": "Source Doctype", "read_only": 1, "search_index": 1}, {"fieldname": "source_docname", "fieldtype": "Data", "label": "Source Docname", "read_only": 1, "search_index": 1}, {"fieldname": "price_list", "fieldtype": "Data", "label": "Price List", "read_only": 1, "search_index": 1}, {"fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1, "search_index": 1}, {"fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rate", "options": "currency", "read_only": 1, "search_index": 1}, {"fieldname": "qty", "fieldtype": "Float", "label": "Qty", "read_only": 1, "search_index": 1}, {"fieldname": "qty_returned", "fieldtype": "Float", "label": "<PERSON><PERSON> Returned", "read_only": 1, "search_index": 1}, {"fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount", "options": "currency", "read_only": 1, "search_index": 1}, {"fieldname": "lrpmt_doctype", "fieldtype": "Data", "label": "LRPMT Doctype", "read_only": 1, "search_index": 1}, {"fieldname": "lrpmt_docname", "fieldtype": "Data", "label": "LRPMT Docname", "read_only": 1, "search_index": 1}, {"fieldname": "dn_detail", "fieldtype": "Data", "label": "Dn Detail", "read_only": 1, "search_index": 1}, {"fieldname": "lrpmt_status", "fieldtype": "Data", "label": "LRPMT Status", "read_only": 1, "search_index": 1}, {"fieldname": "ref_doctype", "fieldtype": "Data", "label": "Ref Doctype", "read_only": 1, "search_index": 1}, {"fieldname": "ref_docname", "fieldtype": "Data", "label": "<PERSON>f <PERSON>", "read_only": 1, "search_index": 1}, {"default": "0", "fieldname": "is_cancelled", "fieldtype": "Check", "label": "Is Cancelled", "read_only": 1, "search_index": 1}, {"fieldname": "insurance_subscription", "fieldtype": "Data", "label": "Insurance Subscription", "read_only": 1, "search_index": 1}, {"fieldname": "insurance_company", "fieldtype": "Data", "label": "Insurance Company", "read_only": 1, "search_index": 1}, {"fieldname": "insurance_coverage_plan", "fieldtype": "Data", "label": "Insurance Coverage Plan", "read_only": 1, "search_index": 1}, {"fieldname": "mode_of_payment", "fieldtype": "Data", "label": "Mode of Payment", "read_only": 1, "search_index": 1}, {"fieldname": "payment_type", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Payment Type", "options": "Cash\nInsurance", "read_only": 1, "search_index": 1}, {"fieldname": "healthcare_practitioner", "fieldtype": "Data", "in_standard_filter": 1, "label": "Healthcare Practitioner", "read_only": 1, "search_index": 1}, {"fieldname": "healthcare_service_unit", "fieldtype": "Data", "in_standard_filter": 1, "label": "Healthcare Service Unit", "read_only": 1, "search_index": 1}, {"fieldname": "department", "fieldtype": "Data", "label": "Department", "read_only": 1, "search_index": 1}, {"fieldname": "cost_center", "fieldtype": "Data", "label": "Cost Center", "read_only": 1, "search_index": 1}, {"fieldname": "created_by", "fieldtype": "Data", "label": "Created By", "read_only": 1, "search_index": 1}, {"fieldname": "column_break_cejb2", "fieldtype": "Column Break"}, {"fieldname": "column_break_0l72r", "fieldtype": "Column Break"}, {"fieldname": "section_break_hx3mw", "fieldtype": "Section Break"}, {"fieldname": "column_break_ckt1u", "fieldtype": "Column Break"}, {"fieldname": "section_break_my8su", "fieldtype": "Section Break"}, {"fieldname": "column_break_9wgfq", "fieldtype": "Column Break"}, {"fieldname": "column_break_iwfua", "fieldtype": "Column Break"}, {"fieldname": "section_break_efosd", "fieldtype": "Section Break"}, {"fieldname": "column_break_pufom", "fieldtype": "Column Break"}, {"fieldname": "section_break_4zy49", "fieldtype": "Section Break"}, {"fieldname": "column_break_nkfo7", "fieldtype": "Column Break"}, {"fieldname": "section_break_frwzc", "fieldtype": "Section Break"}, {"fieldname": "customer", "fieldtype": "Data", "in_standard_filter": 1, "label": "Customer", "read_only": 1, "search_index": 1}, {"fieldname": "sales_invoice", "fieldtype": "Data", "label": "Sales Invoice", "read_only": 1, "search_index": 1}, {"fieldname": "percent_covered", "fieldtype": "Percent", "label": "%Covered", "read_only": 1, "search_index": 1}, {"fieldname": "column_break_eza6d", "fieldtype": "Column Break"}, {"fieldname": "updated_from_doctype", "fieldtype": "Data", "label": "Updated From Doctype", "read_only": 1, "search_index": 1}, {"fieldname": "remarks", "fieldtype": "Small Text", "label": "Remarks", "read_only": 1}, {"fieldname": "updated_by", "fieldtype": "Data", "label": "Updated By", "read_only": 1, "search_index": 1}, {"fieldname": "column_break_gzouw", "fieldtype": "Column Break"}, {"fieldname": "column_break_b5uew", "fieldtype": "Column Break"}, {"fieldname": "updated_from_docname", "fieldtype": "Data", "label": "Updated From Docname", "read_only": 1, "search_index": 1}], "in_create": 1, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-15 23:57:31.109919", "modified_by": "Administrator", "module": "Hms Tz", "name": "Hospital Revenue Entry", "naming_rule": "Random", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}