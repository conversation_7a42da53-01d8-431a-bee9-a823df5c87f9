{"billing.bundle.js": "/assets/frappe/dist/js/billing.bundle.3M2NKQ7F.js", "bootstrap-4-web.bundle.js": "/assets/frappe/dist/js/bootstrap-4-web.bundle.AZ67VXZX.js", "controls.bundle.js": "/assets/frappe/dist/js/controls.bundle.LSXPI5HF.js", "data_import_tools.bundle.js": "/assets/frappe/dist/js/data_import_tools.bundle.F7I46Y5V.js", "desk.bundle.js": "/assets/frappe/dist/js/desk.bundle.V3T34XYE.js", "dialog.bundle.js": "/assets/frappe/dist/js/dialog.bundle.X7QCPWYD.js", "form.bundle.js": "/assets/frappe/dist/js/form.bundle.PCBOED43.js", "frappe-web.bundle.js": "/assets/frappe/dist/js/frappe-web.bundle.4XKJFVOE.js", "libs.bundle.js": "/assets/frappe/dist/js/libs.bundle.LLRFRX7M.js", "list.bundle.js": "/assets/frappe/dist/js/list.bundle.APGDJ4BY.js", "logtypes.bundle.js": "/assets/frappe/dist/js/logtypes.bundle.MJKW7EK3.js", "onboarding_tours.bundle.js": "/assets/frappe/dist/js/onboarding_tours.bundle.P7QYMXLW.js", "report.bundle.js": "/assets/frappe/dist/js/report.bundle.QIIRAYCV.js", "sentry.bundle.js": "/assets/frappe/dist/js/sentry.bundle.SI3DB3BY.js", "telemetry.bundle.js": "/assets/frappe/dist/js/telemetry.bundle.ZJBT5ETW.js", "user_profile_controller.bundle.js": "/assets/frappe/dist/js/user_profile_controller.bundle.TAMQL3L3.js", "video_player.bundle.js": "/assets/frappe/dist/js/video_player.bundle.IOEIXC2G.js", "web_form.bundle.js": "/assets/frappe/dist/js/web_form.bundle.26KCNTHG.js", "form_builder.bundle.js": "/assets/frappe/dist/js/form_builder.bundle.YRYSA3IK.js", "print_format_builder.bundle.js": "/assets/frappe/dist/js/print_format_builder.bundle.23LHQCRJ.js", "workflow_builder.bundle.js": "/assets/frappe/dist/js/workflow_builder.bundle.L3TJGBVY.js", "build_events.bundle.js": "/assets/frappe/dist/js/build_events.bundle.RMXS47QW.js", "file_uploader.bundle.js": "/assets/frappe/dist/js/file_uploader.bundle.NJ4L752J.js", "kanban_board.bundle.js": "/assets/frappe/dist/js/kanban_board.bundle.OUFA2R27.js", "desk.bundle.css": "/assets/frappe/dist/css/desk.bundle.2JILASFQ.css", "email.bundle.css": "/assets/frappe/dist/css/email.bundle.A74LMKY6.css", "login.bundle.css": "/assets/frappe/dist/css/login.bundle.W2TDKPLS.css", "print.bundle.css": "/assets/frappe/dist/css/print.bundle.RFEKV4TQ.css", "print_format.bundle.css": "/assets/frappe/dist/css/print_format.bundle.YB54LGOY.css", "report.bundle.css": "/assets/frappe/dist/css/report.bundle.BB3BLPE2.css", "web_form.bundle.css": "/assets/frappe/dist/css/web_form.bundle.WWQN63BB.css", "website.bundle.css": "/assets/frappe/dist/css/website.bundle.ZNW6BCX7.css", "bank-reconciliation-tool.bundle.js": "/assets/erpnext/dist/js/bank-reconciliation-tool.bundle.G7MHMAFO.js", "erpnext-web.bundle.js": "/assets/erpnext/dist/js/erpnext-web.bundle.253I7LT4.js", "erpnext.bundle.js": "/assets/erpnext/dist/js/erpnext.bundle.Y55VIH4W.js", "item-dashboard.bundle.js": "/assets/erpnext/dist/js/item-dashboard.bundle.Q4W2MCOH.js", "point-of-sale.bundle.js": "/assets/erpnext/dist/js/point-of-sale.bundle.53COWQ5F.js", "bom_configurator.bundle.js": "/assets/erpnext/dist/js/bom_configurator.bundle.PLBYQFQX.js", "erpnext-web.bundle.css": "/assets/erpnext/dist/css/erpnext-web.bundle.Q3FRLULK.css", "erpnext.bundle.css": "/assets/erpnext/dist/css/erpnext.bundle.KTTFQYST.css", "erpnext_email.bundle.css": "/assets/erpnext/dist/css/erpnext_email.bundle.2KQNV2W4.css", "hierarchy-chart.bundle.js": "/assets/hrms/dist/js/hierarchy-chart.bundle.34LONOHB.js", "hrms.bundle.js": "/assets/hrms/dist/js/hrms.bundle.KYSDSR2P.js", "interview.bundle.js": "/assets/hrms/dist/js/interview.bundle.IUBSPKDA.js", "performance.bundle.js": "/assets/hrms/dist/js/performance.bundle.5KYJU75D.js", "hrms.bundle.css": "/assets/hrms/dist/css/hrms.bundle.63RISEXW.css", "wiki.bundle.js": "/assets/wiki/dist/js/wiki.bundle.BBRFFQ57.js", "contributions.bundle.css": "/assets/wiki/dist/css/contributions.bundle.SAI6JSPV.css", "edit_wiki.bundle.css": "/assets/wiki/dist/css/edit_wiki.bundle.JOJOQRVW.css", "wiki.bundle.css": "/assets/wiki/dist/css/wiki.bundle.IUWMFGP6.css", "education.bundle.js": "/assets/education/dist/js/education.bundle.PRLG7BSX.js", "website.bundle.js": "/assets/lms/dist/js/website.bundle.DNOGFJRT.js", "lms.bundle.css": "/assets/lms/dist/css/lms.bundle.FIKL23T2.css", "web.bundle.js": "/assets/webshop/dist/js/web.bundle.UWGVC23C.js", "webshop-web.bundle.css": "/assets/webshop/dist/css/webshop-web.bundle.2V36W6I4.css", "healthcare.bundle.js": "/assets/healthcare/dist/js/healthcare.bundle.WR7I6ZD4.js", "hms_tz.bundle.js": "/assets/hms_tz/dist/js/hms_tz.bundle.WZHMAZ7C.js", "csf_tz.bundle.js": "/assets/csf_tz/dist/js/csf_tz.bundle.X5HX7C4I.js", "erpnext_telegram_integration.bundle.js": "/assets/erpnext_telegram_integration/dist/js/erpnext_telegram_integration.bundle.I5QFNORZ.js", "print_designer.bundle.js": "/assets/print_designer/dist/js/print_designer.bundle.QH7756V6.js", "pdfjs.bundle.css": "/assets/print_designer/dist/css/pdfjs.bundle.PUAAMHER.css", "print_designer.bundle.css": "/assets/print_designer/dist/css/print_designer.bundle.EHC5QZLC.css"}