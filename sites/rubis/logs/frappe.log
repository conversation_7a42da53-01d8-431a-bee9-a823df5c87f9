2025-06-19 17:12:04,289 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'doctype': 'Employee', 'name': 'HR-EMP-00025', 'cmd': 'frappe.client.delete'}
2025-06-19 17:14:46,578 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'doctype': 'Employee', 'name': 'HR-EMP-00025', 'cmd': 'frappe.client.delete'}
2025-06-19 17:19:12,437 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'doctype': 'Employee', 'name': 'HR-EMP-00025', 'cmd': 'frappe.client.delete'}
2025-06-19 17:37:09,315 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'doctype': 'Employee', 'name': 'HR-EMP-00026', 'cmd': 'frappe.client.delete'}
2025-06-19 17:37:52,519 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'doctype': 'Employee', 'name': 'HR-EMP-00026', 'cmd': 'frappe.client.delete'}
2025-07-08 21:20:53,437 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'doc': '{"docstatus":0,"doctype":"Travel Request","name":"new-travel-request-jaugmtsivu","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","travel_type":"Domestic","travel_funding":"Require Full Funding","company":"Rubis Technical Services Limited","itinerary":[{"docstatus":0,"doctype":"Travel Itinerary","name":"new-travel-itinerary-edxvxoytit","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","mode_of_travel":"Bus","meal_preference":"","travel_advance_required":0,"lodging_required":0,"parent":"new-travel-request-jaugmtsivu","parentfield":"itinerary","parenttype":"Travel Request","idx":1,"__unedited":false,"travel_from":"Dar","travel_to":"Arusha","departure_date":"2025-07-08 21:20:13"}],"costings":[{"docstatus":0,"doctype":"Travel Request Costing","name":"new-travel-request-costing-slyjuzbqix","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","parent":"new-travel-request-jaugmtsivu","parentfield":"costings","parenttype":"Travel Request","idx":1,"__unedited":false,"expense_type":"Accommodation","custom_no_of_days":4,"total_amount":120000,"custom_cost_per_day":30000}],"total_travel_cost":120000,"purpose_of_travel":"Site Survey","employee_name":"Daudi Thomas Majinge","cell_number":"0685375814","prefered_email":"<EMAIL>","custom_department":"Technical - A","date_of_birth":"1996-07-03","passport_number":"NA","custom_hod":"<EMAIL>","employee":"HR-EMP-00006","cost_center":"Main - A"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-07-08 21:21:15,653 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'doc': '{"docstatus":0,"doctype":"Travel Request","name":"new-travel-request-jaugmtsivu","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","travel_type":"Domestic","travel_funding":"Require Full Funding","company":"Rubis Technical Services Limited","itinerary":[{"docstatus":0,"doctype":"Travel Itinerary","name":"new-travel-itinerary-edxvxoytit","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","mode_of_travel":"Bus","meal_preference":"","travel_advance_required":0,"lodging_required":0,"parent":"new-travel-request-jaugmtsivu","parentfield":"itinerary","parenttype":"Travel Request","idx":1,"__unedited":false,"travel_from":"Dar","travel_to":"Arusha","departure_date":"2025-07-08 21:20:13"}],"costings":[{"docstatus":0,"doctype":"Travel Request Costing","name":"new-travel-request-costing-slyjuzbqix","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","parent":"new-travel-request-jaugmtsivu","parentfield":"costings","parenttype":"Travel Request","idx":1,"__unedited":false,"expense_type":"Accommodation","custom_no_of_days":4,"total_amount":120000,"custom_cost_per_day":30000}],"total_travel_cost":120000,"purpose_of_travel":"Site Survey","employee_name":"Daudi Thomas Majinge","cell_number":"0685375814","prefered_email":"<EMAIL>","custom_department":"Technical - A","date_of_birth":"1996-07-03","passport_number":"NA","custom_hod":"","employee":"HR-EMP-00006","cost_center":"Main - A"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-07-08 21:24:19,278 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'doc': '{"docstatus":0,"doctype":"Travel Request","name":"new-travel-request-zipjpkkxge","__islocal":1,"__unsaved":1,"owner":"Administrator","travel_type":"Domestic","travel_funding":"Require Full Funding","company":"Rubis Technical Services Limited","itinerary":[{"docstatus":0,"doctype":"Travel Itinerary","name":"new-travel-itinerary-uspiqbxkzo","__islocal":1,"__unsaved":1,"owner":"Administrator","mode_of_travel":"Company Vehicle","meal_preference":"","travel_advance_required":0,"lodging_required":0,"parent":"new-travel-request-zipjpkkxge","parentfield":"itinerary","parenttype":"Travel Request","idx":1,"__unedited":false,"travel_from":"dar","travel_to":"arusha","departure_date":"2025-07-08 21:23:45"}],"costings":[{"docstatus":0,"doctype":"Travel Request Costing","name":"new-travel-request-costing-naucgtjwli","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-travel-request-zipjpkkxge","parentfield":"costings","parenttype":"Travel Request","idx":1,"__unedited":false,"expense_type":"Accommodation","custom_no_of_days":3,"total_amount":150000,"custom_cost_per_day":50000}],"total_travel_cost":150000,"purpose_of_travel":"Out of Office","employee_name":"Daudi Thomas Majinge","cell_number":"0685375814","prefered_email":"<EMAIL>","custom_department":"Technical - A","date_of_birth":"1996-07-03","passport_number":"NA","custom_hod":"<EMAIL>","employee":"HR-EMP-00006"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-07-09 08:55:23,302 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {}
2025-07-12 23:03:31,091 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary 1', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-12 23:07:26,967 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary 1', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-12 23:08:01,688 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary 1', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-12 23:08:27,315 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary 1', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-12 23:10:52,177 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary 1', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-12 23:14:04,292 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary 1', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-12 23:17:41,392 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary 1', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-12 23:24:47,723 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary 1', 'filters': '{"from_date":"2025-01-01","to_date":"2025-07-13","company":"Rubis Technical Services Limited","docstatus":"Submitted"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-12 23:26:53,999 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary 1', 'filters': '{"from_date":"2025-01-01","to_date":"2025-07-13","company":"Rubis Technical Services Limited","docstatus":"Submitted"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-12 23:29:13,399 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary 1', 'filters': '{"from_date":"2025-01-01","to_date":"2025-07-13","company":"Rubis Technical Services Limited","docstatus":"Submitted"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-12 23:30:44,115 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary 1', 'filters': '{"from_date":"2025-01-01","to_date":"2025-07-13","company":"Rubis Technical Services Limited","docstatus":"Submitted"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-12 23:32:17,283 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary 1', 'filters': '{"from_date":"2025-01-01","to_date":"2025-07-13","company":"Rubis Technical Services Limited","docstatus":"Submitted"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-12 23:35:42,122 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary 1', 'filters': '{"from_date":"2025-01-01","to_date":"2025-07-13","company":"Rubis Technical Services Limited","docstatus":"Submitted"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-12 23:37:55,956 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary 1', 'filters': '{"from_date":"2025-01-01","to_date":"2025-07-13","company":"Rubis Technical Services Limited","docstatus":"Submitted"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-12 23:41:59,135 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary 1', 'filters': '{"from_date":"2025-01-01","to_date":"2025-07-13","company":"Rubis Technical Services Limited","docstatus":"Submitted"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-13 10:23:17,909 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'doctype': 'Expense Claim', 'name': 'HR-EXP-2025-00015', 'workflow_state_fieldname': 'workflow_state', 'workflow_state': 'Approved', 'cmd': 'frappe.desk.form.save.cancel'}
2025-07-13 10:28:07,080 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'doctype': 'Salary Slip', 'name': 'Sal Slip/HR-EMP-00024/00003', 'workflow_state_fieldname': 'workflow_state', 'workflow_state': 'Approved by TD', 'cmd': 'frappe.desk.form.save.cancel'}
2025-07-13 10:28:45,678 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'doctype': 'Salary Slip', 'name': 'Sal Slip/HR-EMP-00024/00003', 'workflow_state_fieldname': 'workflow_state', 'workflow_state': 'Approved by TD', 'cmd': 'frappe.desk.form.save.cancel'}
2025-07-13 10:30:31,369 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'doctype': 'Employee', 'name': 'HR-EMP-00025', 'cmd': 'frappe.client.delete'}
2025-07-16 08:55:59,551 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Register Salary', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-16 15:15:51,141 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Employee Advance Sum', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
